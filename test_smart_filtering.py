#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 SMART 信息过滤和红色标注功能
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们修改的模块
import FlashSummary

def test_filter_smart_info():
    """测试 SMART 信息过滤功能"""
    print("=== 测试 SMART 信息过滤功能 ===")
    
    # 测试用例1：包含允许和不允许的字段
    test_smart1 = "05=1234,0C=5678,AF=ABCD,B2=EFGH,XX=9999,C3=1111"
    filtered1 = FlashSummary.FilterSmartInfo(test_smart1)
    print(f"输入: {test_smart1}")
    print(f"过滤后: {filtered1}")
    print(f"预期包含: 05, AF, B2, C3")
    print()
    
    # 测试用例2：只包含允许的字段
    test_smart2 = "B5=2222,B6=3333,C4=4444,C5=5555,C6=6666,C7=7777"
    filtered2 = FlashSummary.FilterSmartInfo(test_smart2)
    print(f"输入: {test_smart2}")
    print(f"过滤后: {filtered2}")
    print(f"预期: 全部保留")
    print()
    
    # 测试用例3：空字符串
    test_smart3 = ""
    filtered3 = FlashSummary.FilterSmartInfo(test_smart3)
    print(f"输入: '{test_smart3}'")
    print(f"过滤后: '{filtered3}'")
    print(f"预期: 空字符串")
    print()
    
    # 测试用例4：不包含允许的字段
    test_smart4 = "XX=1111,YY=2222,ZZ=3333"
    filtered4 = FlashSummary.FilterSmartInfo(test_smart4)
    print(f"输入: {test_smart4}")
    print(f"过滤后: '{filtered4}'")
    print(f"预期: 空字符串")
    print()

def test_has_abnormal_smart_fields():
    """测试异常 SMART 字段检测功能"""
    print("=== 测试异常 SMART 字段检测功能 ===")
    
    # 测试用例1：包含异常字段（非零值）
    test_smart1 = "05=1234,AF=0000,B2=EFGH"
    has_abnormal1 = FlashSummary.HasAbnormalSmartFields(test_smart1)
    print(f"输入: {test_smart1}")
    print(f"检测结果: {has_abnormal1}")
    print(f"预期: True (05和B2是异常字段且非零)")
    print()
    
    # 测试用例2：包含异常字段但值为零
    test_smart2 = "05=0000,B2=00,AF=1234"
    has_abnormal2 = FlashSummary.HasAbnormalSmartFields(test_smart2)
    print(f"输入: {test_smart2}")
    print(f"检测结果: {has_abnormal2}")
    print(f"预期: False (异常字段值为零)")
    print()
    
    # 测试用例3：不包含异常字段
    test_smart3 = "AF=1234,C3=5678,C6=9999"
    has_abnormal3 = FlashSummary.HasAbnormalSmartFields(test_smart3)
    print(f"输入: {test_smart3}")
    print(f"检测结果: {has_abnormal3}")
    print(f"预期: False (不包含异常字段)")
    print()
    
    # 测试用例4：包含多个异常字段
    test_smart4 = "B5=1111,B6=2222,C4=3333,C5=4444"
    has_abnormal4 = FlashSummary.HasAbnormalSmartFields(test_smart4)
    print(f"输入: {test_smart4}")
    print(f"检测结果: {has_abnormal4}")
    print(f"预期: True (包含多个异常字段)")
    print()

def test_get_all_smart_info():
    """测试 GetAllSmartInfo 函数的完整功能"""
    print("=== 测试 GetAllSmartInfo 函数 ===")
    
    # 模拟从 Excel 读取的数据
    test_data = [
        ["Sample001", "05=1234,0C=5678,AF=ABCD,B2=EFGH,XX=9999"],
        ["Sample002", "B5=2222,B6=3333,YY=7777"],
        ["Sample003", "C4=4444,C5=5555,C6=6666,C7=7777"],
        ["Sample004", ""],  # 空 SMART 信息
        ["Sample005", "XX=1111,YY=2222"]  # 不包含允许的字段
    ]
    
    result = FlashSummary.GetAllSmartInfo(test_data)
    print("输入数据:")
    for data in test_data:
        print(f"  {data}")
    print()
    print("处理结果:")
    print(result)
    print()
    print("预期:")
    print("- Sample001: 只包含 05, AF, B2")
    print("- Sample002: 只包含 B5, B6")
    print("- Sample003: 包含 C4, C5, C6, C7")
    print("- Sample004: 跳过（空信息）")
    print("- Sample005: 跳过（无允许字段）")
    print()

if __name__ == "__main__":
    print("开始测试 SMART 信息过滤和异常检测功能\n")
    
    test_filter_smart_info()
    test_has_abnormal_smart_fields()
    test_get_all_smart_info()
    
    print("测试完成！")
