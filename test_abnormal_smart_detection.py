#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的 HasAbnormalSmartFieldsForFlashSummary 函数
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们修改的模块
import PublicFuc

def test_abnormal_smart_detection():
    """测试异常SMART字段检测功能"""
    print("=== 测试异常SMART字段检测功能 ===")
    print(f"异常字段列表: {PublicFuc.flashSummaryAbnormalKey}")
    print()
    
    # 您提供的测试数据
    test_smart_data = """[2P-N58R-01]:  AF=1000000、B6=1、C5=1
[4P-N58R-02]:  AF=1000000、C3=10
[4P-N58R-04]:  AF=1000000、C3=6
[4P-N58R-05]:  AF=5000000、B2=1000000、C3=4、C5=1
[4P-N58R-06]:  C3=347
[4P-N58R-07]:  AF=2000000、B6=1、C3=1A、C4=13、C5=2、C7=5
[4P-N58R-08]:  C3=7
[4P-N58R-11]:  AF=1000000、C3=3A4D、C4=1、C5=1
[4P-N58R-14]:  AF=4170001、C3=304A3、C4=40、C5=1
[4P-N58R-15]:  C3=76
[4P-N58R-18]:  AF=50001、C3=1C637"""
    
    print("测试完整数据:")
    print(test_smart_data)
    print()
    
    result = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_smart_data)
    print(f"整体检测结果: {result}")
    print(f"预期结果: True (因为包含B6=1、C5=1、B2=1000000、C4=13、C4=1、C4=40等异常字段)")
    print()
    
    # 逐行测试
    print("=== 逐行测试 ===")
    lines = test_smart_data.strip().split('\n')
    
    for i, line in enumerate(lines, 1):
        result = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(line)
        print(f"第{i:2d}行: {result} - {line}")
        
        # 分析每行包含的异常字段
        import re
        pattern = r'([A-F0-9]+)=([A-F0-9]+)'
        matches = re.findall(pattern, line.upper())
        
        abnormal_fields = []
        for field_id, value in matches:
            if field_id in PublicFuc.flashSummaryAbnormalKey:
                try:
                    # 处理十六进制值
                    if value.startswith('0X'):
                        int_value = int(value, 16)
                    else:
                        try:
                            int_value = int(value, 16)
                        except ValueError:
                            int_value = int(value, 10)
                    
                    if int_value != 0:
                        abnormal_fields.append(f"{field_id}={value}")
                except:
                    if value and value != '0' and value != '00':
                        abnormal_fields.append(f"{field_id}={value}")
        
        if abnormal_fields:
            print(f"        异常字段: {', '.join(abnormal_fields)}")
        else:
            print(f"        无异常字段")
        print()

def test_individual_cases():
    """测试单独的案例"""
    print("=== 测试单独案例 ===")
    
    test_cases = [
        # (测试字符串, 预期结果, 描述)
        ("AF=1000000、B6=1、C5=1", True, "包含B6=1和C5=1异常字段"),
        ("AF=1000000、C3=10", False, "只包含正常字段"),
        ("B2=1000000、C3=4、C5=1", True, "包含B2=1000000和C5=1异常字段"),
        ("C3=347", False, "只包含正常字段C3"),
        ("AF=2000000、B6=1、C3=1A、C4=13、C5=2、C7=5", True, "包含B6=1、C4=13、C5=2异常字段"),
        ("C3=7", False, "只包含正常字段C3"),
        ("AF=1000000、C3=3A4D、C4=1、C5=1", True, "包含C4=1和C5=1异常字段"),
        ("AF=4170001、C3=304A3、C4=40、C5=1", True, "包含C4=40和C5=1异常字段"),
        ("C3=76", False, "只包含正常字段C3"),
        ("AF=50001、C3=1C637", False, "只包含正常字段"),
        ("05=1234、B5=5678", True, "包含05=1234和B5=5678异常字段"),
        ("05=0、B5=0", False, "异常字段值为0，不标注"),
        ("", False, "空字符串"),
    ]
    
    for test_input, expected, description in test_cases:
        result = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_input)
        status = "✓" if result == expected else "✗"
        print(f"{status} 输入: '{test_input}'")
        print(f"   结果: {result}, 预期: {expected}")
        print(f"   描述: {description}")
        print()

def test_regex_pattern():
    """测试正则表达式匹配"""
    print("=== 测试正则表达式匹配 ===")
    
    import re
    pattern = r'([A-F0-9]+)=([A-F0-9]+)'
    
    test_strings = [
        "[2P-N58R-01]:  AF=1000000、B6=1、C5=1",
        "AF=1000000、C3=10",
        "B2=1000000、C3=4、C5=1",
        "AF=2000000、B6=1、C3=1A、C4=13、C5=2、C7=5",
        "C3=3A4D、C4=1、C5=1",
    ]
    
    for test_str in test_strings:
        matches = re.findall(pattern, test_str.upper())
        print(f"字符串: {test_str}")
        print(f"匹配结果: {matches}")
        
        # 检查异常字段
        abnormal_count = 0
        for field_id, value in matches:
            if field_id in PublicFuc.flashSummaryAbnormalKey:
                abnormal_count += 1
                print(f"  异常字段: {field_id}={value}")
        
        if abnormal_count == 0:
            print(f"  无异常字段")
        print()

if __name__ == "__main__":
    print("开始测试修改后的 HasAbnormalSmartFieldsForFlashSummary 函数\n")
    
    test_regex_pattern()
    test_individual_cases()
    test_abnormal_smart_detection()
    
    print("测试完成！")
    print("\n=== 总结 ===")
    print("1. 函数现在使用正则表达式匹配所有 '字段=值' 模式")
    print("2. 支持多行格式和复杂的样品编号格式")
    print("3. 能够正确识别异常字段：05、B2、B5、B6、C4、C5")
    print("4. 支持十六进制和十进制值的解析")
    print("5. 只有当异常字段值不为0时才返回True")
