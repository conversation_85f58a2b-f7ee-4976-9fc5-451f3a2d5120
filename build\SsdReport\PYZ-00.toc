('G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\build\\SsdReport\\PYZ-00.pyz',
 [('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ssl.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\struct.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_strptime.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\getopt.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\selectors.py',
   'PYMODULE'),
  ('pkg_resources',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('winerror',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('pywintypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('site',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\fake-modules\\site.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\contextlib.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('win32com.server',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.build',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\string.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pprint.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\signal.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\fnmatch.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\webbrowser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\random.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bisect.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\hashlib.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\optparse.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tty.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\subprocess.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\runpy.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\shlex.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\codeop.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\cmd.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\difflib.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\glob.py',
   'PYMODULE'),
  ('win32com.client.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\uuid.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('netbios',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bz2.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('lib2to3.refactor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\refactor.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\netrc.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\numbers.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('lib2to3.btm_matcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\btm_matcher.py',
   'PYMODULE'),
  ('lib2to3.btm_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\btm_utils.py',
   'PYMODULE'),
  ('lib2to3.pgen2.grammar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\grammar.py',
   'PYMODULE'),
  ('lib2to3.pygram',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pygram.py',
   'PYMODULE'),
  ('lib2to3.pytree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pytree.py',
   'PYMODULE'),
  ('lib2to3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\__init__.py',
   'PYMODULE'),
  ('lib2to3.patcomp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\patcomp.py',
   'PYMODULE'),
  ('lib2to3.pgen2.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\parse.py',
   'PYMODULE'),
  ('lib2to3.pgen2.literals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\literals.py',
   'PYMODULE'),
  ('lib2to3.fixer_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\fixer_util.py',
   'PYMODULE'),
  ('lib2to3.pgen2.token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\token.py',
   'PYMODULE'),
  ('lib2to3.pgen2.tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\tokenize.py',
   'PYMODULE'),
  ('lib2to3.pgen2.driver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\driver.py',
   'PYMODULE'),
  ('lib2to3.pgen2.pgen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\pgen.py',
   'PYMODULE'),
  ('lib2to3.pgen2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\py_compile.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\imp.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\posixpath.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\genericpath.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ntpath.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ast.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\plistlib.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pkgutil.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\stat.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\zipfile.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\__future__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\stringprep.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\typing.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('win32com.client',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('FlashSummary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\FlashSummary.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('jdcal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\jdcal.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('pandas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.arrays',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.ops.roperator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\roperator.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_testing.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.path',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('numpy.linalg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pathlib.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.records',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy._globals',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.lib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.financial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\financial.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.dual',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\dual.py',
   'PYMODULE'),
  ('numpy.fft',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\fcompiler\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.config_compiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\config_compiler.py',
   'PYMODULE'),
  ('numpy.distutils.command',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.egg_info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.extension',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.command',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.dist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.depends',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.py33compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\py33compat.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_markupbase.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.pep425tags',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\pep425tags.py',
   'PYMODULE'),
  ('setuptools.glibc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\glibc.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.ssl_support',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.py27compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\py27compat.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\configparser.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\six.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools.namespaces',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\namespaces.py',
   'PYMODULE'),
  ('setuptools.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools.extern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.develop',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\develop.py',
   'PYMODULE'),
  ('setuptools.command.develop',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\develop.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_clib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.bdist_rpm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools.command.bdist_rpm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('numpy.distutils.command.install',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools.command.install',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\install.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_headers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('numpy.distutils.command.sdist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_scripts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_clib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_ext',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('numpy.distutils.system_info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\system_info.py',
   'PYMODULE'),
  ('numpy.f2py',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py_testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\f2py_testing.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_src',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_src.py',
   'PYMODULE'),
  ('numpy.distutils.conv_template',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\conv_template.py',
   'PYMODULE'),
  ('numpy.distutils.from_template',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\from_template.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_py',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('numpy.distutils.command.build',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('numpy.distutils.command.config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\config.py',
   'PYMODULE'),
  ('numpy.distutils.command.autodist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\autodist.py',
   'PYMODULE'),
  ('numpy.distutils.mingw32ccompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\mingw32ccompiler.py',
   'PYMODULE'),
  ('distutils.msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\cgi.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('numpy.distutils.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\core.py',
   'PYMODULE'),
  ('numpy.distutils.numpy_distribution',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\numpy_distribution.py',
   'PYMODULE'),
  ('numpy.distutils.extension',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\extension.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler.environment',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\fcompiler\\environment.py',
   'PYMODULE'),
  ('numpy.distutils.exec_command',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\exec_command.py',
   'PYMODULE'),
  ('numpy.distutils.misc_util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\misc_util.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('numpy.distutils.__config__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\__config__.py',
   'PYMODULE'),
  ('numpy.distutils.npy_pkg_config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\npy_pkg_config.py',
   'PYMODULE'),
  ('numpy.distutils.unixccompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils._shell_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\_shell_utils.py',
   'PYMODULE'),
  ('pipes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pipes.py',
   'PYMODULE'),
  ('numpy.distutils.lib2def',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\lib2def.py',
   'PYMODULE'),
  ('numpy.distutils.log',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\log.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_template',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_template.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_svg.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\afm.py',
   'PYMODULE'),
  ('pyparsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pyparsing.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.fontconfig_pattern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.units',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('dateutil.tz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.easter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.parser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil._common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\fractions.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_mixed',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_mixed.py',
   'PYMODULE'),
  ('matplotlib.tight_bbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tight_bbox.py',
   'PYMODULE'),
  ('PIL.Image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._binary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_ps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_ps.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib._text_layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_text_layout.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_pgf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_pgf.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_pdf.py',
   'PYMODULE'),
  ('matplotlib.type1font',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\type1font.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_pdf_ps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_backend_pdf_ps.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('matplotlib.tri.triangulation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri.triplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\triplot.py',
   'PYMODULE'),
  ('matplotlib.tri.tripcolor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri.trirefine',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri.triinterpolate',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri.tritools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\tritools.py',
   'PYMODULE'),
  ('matplotlib.tri.tricontour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\tricontour.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.blocking_input',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\blocking_input.py',
   'PYMODULE'),
  ('matplotlib.tri.trifinder',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\trifinder.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.container',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.category',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.axes._subplots',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_subplots.py',
   'PYMODULE'),
  ('matplotlib._layoutbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_layoutbox.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.tight_layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tight_layout.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.docstring',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\docstring.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\cbook\\__init__.py',
   'PYMODULE'),
  ('matplotlib.cbook.deprecation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\cbook\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib.style',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.animation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\animation.py',
   'PYMODULE'),
  ('matplotlib._animation_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_animation_data.py',
   'PYMODULE'),
  ('matplotlib.image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('certifi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('matplotlib._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('cycler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\cycler.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('numpy.random',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\parsers.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.api.types',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.window',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.indexers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\indexers.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.plotting',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\compat.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.core.series',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dataclasses.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.core.aggregation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\aggregation.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\strings.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexers.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._config.config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\csv.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pytz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.html',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('Flash_InitPerformance',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Flash_InitPerformance.py',
   'PYMODULE'),
  ('Flash_Reliability',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Flash_Reliability.py',
   'PYMODULE'),
  ('Performance2',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Performance2.py',
   'PYMODULE'),
  ('IdlePerformance',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\IdlePerformance.py',
   'PYMODULE'),
  ('cProfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\cProfile.py',
   'PYMODULE'),
  ('pstats',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pstats.py',
   'PYMODULE'),
  ('profile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\profile.py',
   'PYMODULE'),
  ('Summary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Summary.py',
   'PYMODULE'),
  ('FactoryWAF',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\FactoryWAF.py',
   'PYMODULE'),
  ('Format',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Format.py',
   'PYMODULE'),
  ('PerformanceDingRong',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\PerformanceDingRong.py',
   'PYMODULE'),
  ('NewItem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\NewItem.py',
   'PYMODULE'),
  ('NormalBit',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\NormalBit.py',
   'PYMODULE'),
  ('CRCTest',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\CRCTest.py',
   'PYMODULE'),
  ('Reliability',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Reliability.py',
   'PYMODULE'),
  ('Stability',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Stability.py',
   'PYMODULE'),
  ('RS_Performance',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\RS_Performance.py',
   'PYMODULE'),
  ('Performance',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\Performance.py',
   'PYMODULE'),
  ('PublicFuc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\PublicFuc.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('requests',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.request',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.response',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.hooks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.models',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.__version__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('chardet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('urllib3',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('openpyxl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tempfile.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\shutil.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\os.py',
   'PYMODULE')])
