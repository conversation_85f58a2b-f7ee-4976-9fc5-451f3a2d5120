import PublicFuc, re, copy
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import PatternFill, Alignment, Border, Side, colors, Font
from datetime import datetime, timedelta


def Run(curpath, workBook, alignment):
    ws = workBook['主页']
    ws.alignment = alignment
    ProHTempBurnin48H(ws, workBook)
    ProHTempBurnin72H(ws, workBook)
    ProATempBurin(ws, workBook)
    
    ProHTempReadDisturb48H(ws, workBook)
    ProHTempReadDisturb72H(ws, workBook)
    ProATempReadDisturb(ws, workBook)
    
    ProHDataRemindP(ws, workBook)
    ProHDataRemindL(ws, workBook)
    ProHWriteLRead(ws, workBook)
    ProLWriteHRead(ws, workBook)
    ProCDMPicture(ws, workBook)
    ProASSDPicture(ws, workBook)


# 过滤SMART信息，只保留指定的字段
def FilterSmartInfo(smartStr):
    """
    过滤SMART信息，只保留指定的字段：05、AF、B2、B5、B6、C3、C4、C5、C6、C7
    """
    if not smartStr or smartStr.strip() == '':
        return ''

    # 定义需要保留的SMART字段
    allowed_smart_fields = ['05', 'AF', 'B2', 'B5', 'B6', 'C3', 'C4', 'C5', 'C6', 'C7']

    # 分割SMART信息字符串，通常格式为 "05=xxx,AF=yyy,B2=zzz"
    smart_pairs = smartStr.split(',')
    filtered_pairs = []

    for pair in smart_pairs:
        pair = pair.strip()
        if '=' in pair:
            field_id = pair.split('=')[0].strip().upper()
            if field_id in allowed_smart_fields:
                filtered_pairs.append(pair)

    return ','.join(filtered_pairs)

# 检测异常SMART字段
def HasAbnormalSmartFields(smartStr):
    """
    检测SMART信息中是否包含异常字段：05、B2、B5、B6、C4、C5
    返回True表示包含异常字段，需要红色标注
    """
    if not smartStr or smartStr.strip() == '':
        return False

    # 定义异常SMART字段
    abnormal_smart_fields = ['05', 'B2', 'B5', 'B6', 'C4', 'C5']

    # 分割SMART信息字符串
    smart_pairs = smartStr.split(',')

    for pair in smart_pairs:
        pair = pair.strip()
        if '=' in pair:
            field_id = pair.split('=')[0].strip().upper()
            if field_id in abnormal_smart_fields:
                # 检查值是否不为0（表示异常）
                try:
                    value_str = pair.split('=')[1].strip()
                    if value_str and value_str != '0' and value_str != '00':
                        return True
                except:
                    continue

    return False

# 获取测试失败的mark信息
def GetAllSmartInfo(dataList):
    strResult = ''
    for line in dataList:
        if len(line) < 2:
            continue
        # 过滤SMART信息
        filtered_smart = FilterSmartInfo(line[1])
        if filtered_smart:  # 只有当过滤后还有内容时才添加
            strResult = strResult + '[' + line[0] + ']:  ' + filtered_smart.replace(',','、')
            strResult = strResult + '\r\n'
    return strResult


def GetSpecialDataCnt(_startRow, _endRow, _colName, ws, step=1):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow, _endRow + 1, step):
        celPosSample = '%s%d' % (_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt


# 判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False

    isValid = False
    for idx in range(1, len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid


# 获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow, _endRow, _colNameList, ws, step=1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow, _endRow + 1, step):
        oneRow = []
        for _colName in _colNameList:
            if _colName == '':
                oneRow.append('')
            else:
                celPosSample = '%s%d' % (_colName, rowNo)
                celValue = ws[celPosSample].value
                oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic


# 获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialSingleColDataList(_startRow, _endRow, _colName, ws, step=1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow, _endRow + 1, step):
        oneRow = []
        celPosSample = '%s%d' % (_colName, rowNo)
        celValue = ws[celPosSample].value
        dic.append(celValue)
    return dic


def ProHTempBurnin48H(ws, workBook):
    wsTmp = workBook['A1_高温老化_48H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L5'] = ''
    ws['L5'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L5'].fill = PatternFill('solid', fgColor='FF0000')


def ProHTempBurnin72H(ws, workBook):
    wsTmp = workBook['B1_高温老化_72H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L10'] = ''
    ws['L10'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L10'].fill = PatternFill('solid', fgColor='FF0000')

def ProATempBurin(ws, workBook):
    wsTmp = workBook['C1_常温老化']
    MIN_LINE = 11
    MAX_LINE = 210
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L13'] = ''
    ws['L13'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L13'].fill = PatternFill('solid', fgColor='FF0000')

def ProHTempReadDisturb48H(ws, workBook):
    wsTmp = workBook['A2_高温ReadDisturb_48H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'S']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L6'] = ''
    ws['L6'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L6'].fill = PatternFill('solid', fgColor='FF0000')


def ProHTempReadDisturb72H(ws, workBook):
    wsTmp = workBook['B2_高温ReadDisturb_72H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'S']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L11'] = ''
    ws['L11'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L11'].fill = PatternFill('solid', fgColor='FF0000')

def ProATempReadDisturb(ws, workBook):
    wsTmp = workBook['C2_常温ReadDisturb']
    MIN_LINE = 10
    MAX_LINE = 109
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'S']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L14'] = ''
    ws['L14'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L14'].fill = PatternFill('solid', fgColor='FF0000')

def ProHDataRemindP(ws, workBook):
    wsTmp = workBook['A3_高温数据保持_逻辑盘']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L7'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L7'].fill = PatternFill('solid', fgColor='FF0000')


def ProHDataRemindL(ws, workBook):
    wsTmp = workBook['B3_高温数据保持_物理盘']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L12'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L12'].fill = PatternFill('solid', fgColor='FF0000')


def ProHWriteLRead(ws, workBook):
    wsTmp = workBook['A4_高写低读、低写高读_逻辑盘']
    MIN_LINE = 9
    MAX_LINE = 18
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L8'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L8'].fill = PatternFill('solid', fgColor='FF0000')


def ProLWriteHRead(ws, workBook):
    wsTmp = workBook['A4_高写低读、低写高读_逻辑盘']
    MIN_LINE = 22
    MAX_LINE = 31
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    ws['L9'] = smartInfo

    # 检查是否有异常SMART字段，如果有则应用红色标注
    if HasAbnormalSmartFields(smartInfo):
        ws['L9'].fill = PatternFill('solid', fgColor='FF0000')


def ProCDMPicture(ws, workBook):
    wsTmp = workBook['A5_初始性能测试']
    MIN_LINE = 7
    MAX_LINE = 16
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)

    sampleNo = GetSpecialSingleColDataList(MIN_LINE, MAX_LINE, 'C', wsTmp)
    if len(sampleNo) < 1:
        return

    # for i in range(len(sampleNo)):
    #     pos = '%s%d'%(get_column_letter(2+i*7), 13)
    #     ws[pos] = sampleNo[i]
    num = 0
    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor == 'A17':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'B17')
        elif image.anchor == 'D17':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I17')

def ProASSDPicture(ws, workBook):
    wsTmp = workBook['A5_初始性能测试']
    MIN_LINE = 21
    MAX_LINE = 30
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)

    sampleNo = GetSpecialSingleColDataList(MIN_LINE, MAX_LINE, 'C', wsTmp)
    if len(sampleNo) < 1:
        return

    # for i in range(len(sampleNo)):
    #     pos = '%s%d'%(get_column_letter(2+i*7), 17)
    #     ws[pos] = sampleNo[i]
    num = 0
    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor == 'A31':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'B19')
        elif image.anchor == 'D31':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I19')
