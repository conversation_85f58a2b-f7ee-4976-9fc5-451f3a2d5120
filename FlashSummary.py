import PublicFuc, re, copy
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import PatternFill, Alignment, Border, Side, colors, Font
from datetime import datetime, timedelta

# 尝试导入富文本模块，支持不同版本的 openpyxl
try:
    # 尝试新版本的富文本模块 (openpyxl 3.1.0+)
    from openpyxl.cell.rich_text import TextBlock, CellRichText
    # 同时导入 InlineFont，3.1.0+ 版本的 TextBlock 需要 InlineFont
    from openpyxl.cell.text import InlineFont
    RICH_TEXT_SUPPORTED = True
    RICH_TEXT_VERSION = "3.1+"
    print(f"检测到 openpyxl 3.1+ 版本，启用完整富文本功能")
except ImportError:
    try:
        # 尝试旧版本的富文本模块 (openpyxl 3.0.x)
        from openpyxl.cell.text import RichText, InlineFont, Text
        RICH_TEXT_SUPPORTED = True
        RICH_TEXT_VERSION = "3.0.x"
        print(f"检测到 openpyxl 3.0.x 版本，使用标记文本方案")
    except ImportError:
        # 如果都不支持富文本，使用备用方案
        RICH_TEXT_SUPPORTED = False
        RICH_TEXT_VERSION = "none"
        print("警告: 当前 openpyxl 版本不支持富文本功能，将使用备用的单元格背景标红方案")


def Run(curpath, workBook, alignment):
    ws = workBook['主页']
    ws.alignment = alignment
    ProHTempBurnin48H(ws, workBook)
    ProHTempBurnin72H(ws, workBook)
    ProATempBurin(ws, workBook)
    
    ProHTempReadDisturb48H(ws, workBook)
    ProHTempReadDisturb72H(ws, workBook)
    ProATempReadDisturb(ws, workBook)
    
    ProHDataRemindP(ws, workBook)
    ProHDataRemindL(ws, workBook)
    ProHWriteLRead(ws, workBook)
    ProLWriteHRead(ws, workBook)
    ProCDMPicture(ws, workBook)
    ProASSDPicture(ws, workBook)


# 创建带有红色字体标注的富文本或标记文本
def CreateRichTextWithRedAbnormalFields(smartInfo):
    """
    创建富文本，将异常SMART字段标红
    如果不支持富文本，则返回带标记的普通文本
    smartInfo: SMART信息字符串
    返回: CellRichText对象或普通字符串
    """
    if not smartInfo or smartInfo.strip() == '':
        return smartInfo

    if RICH_TEXT_SUPPORTED:
        if RICH_TEXT_VERSION == "3.1+":
            # 新版本 openpyxl 3.1.0+ 的富文本处理
            rich_text = CellRichText()

            # 定义红色字体（3.1.0+ 版本的 TextBlock 需要使用 InlineFont）
            red_font = InlineFont(color='FF0000')  # 红色字体
            normal_font = InlineFont()  # 默认字体

            # 使用正则表达式查找所有 "关键字=值" 的模式
            pattern = r'([A-F0-9]+)=([A-F0-9]+)'

            last_end = 0
            text = smartInfo

            for match in re.finditer(pattern, text):
                start, end = match.span()
                field_id = match.group(1).upper()
                full_match = match.group(0)  # 完整的 "关键字=值"

                # 添加匹配前的普通文本
                if start > last_end:
                    normal_text = text[last_end:start]
                    if normal_text:
                        rich_text.append(TextBlock(normal_font, normal_text))

                # 检查是否是异常字段
                if field_id in PublicFuc.flashSummaryAbnormalKey:
                    # 异常字段用红色字体
                    rich_text.append(TextBlock(red_font, full_match))
                else:
                    # 正常字段用默认字体
                    rich_text.append(TextBlock(normal_font, full_match))

                last_end = end

            # 添加剩余的普通文本
            if last_end < len(text):
                remaining_text = text[last_end:]
                if remaining_text:
                    rich_text.append(TextBlock(normal_font, remaining_text))

            return rich_text

        elif RICH_TEXT_VERSION == "3.0.x":
            # openpyxl 3.0.x 的富文本实现较为复杂，暂时使用标记文本方案
            # 在异常字段前后添加特殊标记，便于识别
            pattern = r'([A-F0-9]+)=([A-F0-9]+)'

            def replace_abnormal_fields(match):
                field_id = match.group(1).upper()
                full_match = match.group(0)

                if field_id in PublicFuc.flashSummaryAbnormalKey:
                    # 异常字段添加标记
                    return f"【{full_match}】"
                else:
                    return full_match

            marked_text = re.sub(pattern, replace_abnormal_fields, smartInfo)
            return marked_text
    else:
        # 不支持富文本时，返回带标记的普通文本
        # 在异常字段前后添加特殊标记，便于识别
        pattern = r'([A-F0-9]+)=([A-F0-9]+)'

        def replace_abnormal_fields(match):
            field_id = match.group(1).upper()
            full_match = match.group(0)

            if field_id in PublicFuc.flashSummaryAbnormalKey:
                # 异常字段添加标记
                return f"【{full_match}】"
            else:
                return full_match

        marked_text = re.sub(pattern, replace_abnormal_fields, smartInfo)
        return marked_text

# 辅助函数：设置单元格的SMART信息并应用异常字段标注
def SetSmartInfoWithAnnotation(ws, cell_address, smartInfo):
    """
    设置单元格的SMART信息并应用异常字段标注
    ws: 工作表对象
    cell_address: 单元格地址（如'L5'）
    smartInfo: SMART信息字符串
    """
    ws[cell_address] = ''

    # 检查是否有异常SMART字段，如果有则应用标注
    if PublicFuc.HasAbnormalSmartFieldsForFlashSummary(smartInfo):
        # 由于富文本在保存时可能出现兼容性问题，统一使用背景标红方案
        ws[cell_address] = smartInfo
        ws[cell_address].fill = PatternFill('solid', fgColor='FF0000')
    else:
        ws[cell_address] = smartInfo

# 获取测试失败的mark信息 - 专门用于Flash品质测试用例及报告汇总.xlsx
def GetAllSmartInfo(dataList):
    strResult = ''
    for line in dataList:
        if len(line) < 2:
            continue
        # 使用PublicFuc中专门的过滤函数
        filtered_smart = PublicFuc.FilterSmartInfoForFlashSummary(line[1])
        if filtered_smart:  # 只有当过滤后还有内容时才添加
            strResult = strResult + '[' + line[0] + ']:  ' + filtered_smart.replace(',','、')
            strResult = strResult + '\r\n'
    return strResult


def GetSpecialDataCnt(_startRow, _endRow, _colName, ws, step=1):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow, _endRow + 1, step):
        celPosSample = '%s%d' % (_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt


# 判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False

    isValid = False
    for idx in range(1, len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid


# 获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow, _endRow, _colNameList, ws, step=1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow, _endRow + 1, step):
        oneRow = []
        for _colName in _colNameList:
            if _colName == '':
                oneRow.append('')
            else:
                celPosSample = '%s%d' % (_colName, rowNo)
                celValue = ws[celPosSample].value
                oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic


# 获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialSingleColDataList(_startRow, _endRow, _colName, ws, step=1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow, _endRow + 1, step):
        oneRow = []
        celPosSample = '%s%d' % (_colName, rowNo)
        celValue = ws[celPosSample].value
        dic.append(celValue)
    return dic


def ProHTempBurnin48H(ws, workBook):
    wsTmp = workBook['A1_高温老化_48H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L5', smartInfo)


def ProHTempBurnin72H(ws, workBook):
    wsTmp = workBook['B1_高温老化_72H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L10', smartInfo)

def ProATempBurin(ws, workBook):
    wsTmp = workBook['C1_常温老化']
    MIN_LINE = 11
    MAX_LINE = 210
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L13', smartInfo)

def ProHTempReadDisturb48H(ws, workBook):
    wsTmp = workBook['A2_高温ReadDisturb_48H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'S']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L6', smartInfo)


def ProHTempReadDisturb72H(ws, workBook):
    wsTmp = workBook['B2_高温ReadDisturb_72H']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'S']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L11', smartInfo)

def ProATempReadDisturb(ws, workBook):
    wsTmp = workBook['C2_常温ReadDisturb']
    MIN_LINE = 10
    MAX_LINE = 109
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)
    colList = ['C', 'S']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L14', smartInfo)

def ProHDataRemindP(ws, workBook):
    wsTmp = workBook['A3_高温数据保持_逻辑盘']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L7', smartInfo)


def ProHDataRemindL(ws, workBook):
    wsTmp = workBook['B3_高温数据保持_物理盘']
    MIN_LINE = 8
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L12', smartInfo)


def ProHWriteLRead(ws, workBook):
    wsTmp = workBook['A4_高写低读、低写高读_逻辑盘']
    MIN_LINE = 9
    MAX_LINE = 18
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L8', smartInfo)


def ProLWriteHRead(ws, workBook):
    wsTmp = workBook['A4_高写低读、低写高读_逻辑盘']
    MIN_LINE = 22
    MAX_LINE = 31
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'B', wsTmp)
    colList = ['B', 'O']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)

    if totalSampleCnt < 1:
        return

    smartInfo = GetAllSmartInfo(dataList)
    SetSmartInfoWithAnnotation(ws, 'L9', smartInfo)


def ProCDMPicture(ws, workBook):
    wsTmp = workBook['A5_初始性能测试']
    MIN_LINE = 7
    MAX_LINE = 16
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)

    sampleNo = GetSpecialSingleColDataList(MIN_LINE, MAX_LINE, 'C', wsTmp)
    if len(sampleNo) < 1:
        return

    # for i in range(len(sampleNo)):
    #     pos = '%s%d'%(get_column_letter(2+i*7), 13)
    #     ws[pos] = sampleNo[i]
    num = 0
    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor == 'A17':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'B17')
        elif image.anchor == 'D17':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I17')

def ProASSDPicture(ws, workBook):
    wsTmp = workBook['A5_初始性能测试']
    MIN_LINE = 21
    MAX_LINE = 30
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)

    sampleNo = GetSpecialSingleColDataList(MIN_LINE, MAX_LINE, 'C', wsTmp)
    if len(sampleNo) < 1:
        return

    # for i in range(len(sampleNo)):
    #     pos = '%s%d'%(get_column_letter(2+i*7), 17)
    #     ws[pos] = sampleNo[i]
    num = 0
    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor == 'A31':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'B19')
        elif image.anchor == 'D31':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I19')
