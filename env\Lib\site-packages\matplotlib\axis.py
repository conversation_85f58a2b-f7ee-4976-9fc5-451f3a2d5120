"""
Classes for the ticks and x and y axis.
"""

import datetime
import functools
import logging

import numpy as np

import matplotlib as mpl
import matplotlib.artist as martist
import matplotlib.cbook as cbook
import matplotlib.lines as mlines
import matplotlib.scale as mscale
import matplotlib.text as mtext
import matplotlib.ticker as mticker
import matplotlib.transforms as mtransforms
import matplotlib.units as munits

_log = logging.getLogger(__name__)

GRIDLINE_INTERPOLATION_STEPS = 180

# This list is being used for compatibility with Axes.grid, which
# allows all Line2D kwargs.
_line_inspector = martist.ArtistInspector(mlines.Line2D)
_line_param_names = _line_inspector.get_setters()
_line_param_aliases = [list(d)[0] for d in _line_inspector.aliasd.values()]
_gridline_param_names = ['grid_' + name
                         for name in _line_param_names + _line_param_aliases]


class Tick(martist.Artist):
    """
    Abstract base class for the axis ticks, grid lines and labels.

    Ticks mark a position on an Axis. They contain two lines as markers and
    two labels; one each for the bottom and top positions (in case of an
    `.XAxis`) or for the left and right positions (in case of a `.YAxis`).

    Attributes
    ----------
    tick1line : `.Line2D`
        The left/bottom tick marker.
    tick2line : `.Line2D`
        The right/top tick marker.
    gridline : `.Line2D`
        The grid line associated with the label position.
    label1 : `.Text`
        The left/bottom tick label.
    label2 : `.Text`
        The right/top tick label.

    """
    @cbook._delete_parameter("3.3", "label")
    def __init__(self, axes, loc, label=None,
                 size=None,  # points
                 width=None,
                 color=None,
                 tickdir=None,
                 pad=None,
                 labelsize=None,
                 labelcolor=None,
                 zorder=None,
                 gridOn=None,  # defaults to axes.grid depending on
                               # axes.grid.which
                 tick1On=True,
                 tick2On=True,
                 label1On=True,
                 label2On=False,
                 major=True,
                 labelrotation=0,
                 grid_color=None,
                 grid_linestyle=None,
                 grid_linewidth=None,
                 grid_alpha=None,
                 **kw  # Other Line2D kwargs applied to gridlines.
                 ):
        """
        bbox is the Bound2D bounding box in display coords of the Axes
        loc is the tick location in data coords
        size is the tick size in points
        """
        martist.Artist.__init__(self)

        if gridOn is None:
            if major and (mpl.rcParams['axes.grid.which']
                          in ('both', 'major')):
                gridOn = mpl.rcParams['axes.grid']
            elif (not major) and (mpl.rcParams['axes.grid.which']
                                  in ('both', 'minor')):
                gridOn = mpl.rcParams['axes.grid']
            else:
                gridOn = False

        self.set_figure(axes.figure)
        self.axes = axes

        name = self.__name__.lower()

        self._loc = loc
        self._major = major

        major_minor = "major" if major else "minor"

        if size is None:
            size = mpl.rcParams[f"{name}.{major_minor}.size"]
        self._size = size

        if width is None:
            width = mpl.rcParams[f"{name}.{major_minor}.width"]
        self._width = width

        if color is None:
            color = mpl.rcParams[f"{name}.color"]

        if pad is None:
            pad = mpl.rcParams[f"{name}.{major_minor}.pad"]
        self._base_pad = pad

        if labelcolor is None:
            labelcolor = mpl.rcParams[f"{name}.color"]

        if labelsize is None:
            labelsize = mpl.rcParams[f"{name}.labelsize"]

        self._set_labelrotation(labelrotation)

        if zorder is None:
            if major:
                zorder = mlines.Line2D.zorder + 0.01
            else:
                zorder = mlines.Line2D.zorder
        self._zorder = zorder

        if grid_color is None:
            grid_color = mpl.rcParams["grid.color"]
        if grid_linestyle is None:
            grid_linestyle = mpl.rcParams["grid.linestyle"]
        if grid_linewidth is None:
            grid_linewidth = mpl.rcParams["grid.linewidth"]
        if grid_alpha is None:
            grid_alpha = mpl.rcParams["grid.alpha"]
        grid_kw = {k[5:]: v for k, v in kw.items()}

        self.apply_tickdir(tickdir)

        self.tick1line = mlines.Line2D(
            [], [],
            color=color, linestyle="none", zorder=zorder, visible=tick1On,
            markeredgecolor=color, markersize=size, markeredgewidth=width,
        )
        self.tick2line = mlines.Line2D(
            [], [],
            color=color, linestyle="none", zorder=zorder, visible=tick2On,
            markeredgecolor=color, markersize=size, markeredgewidth=width,
        )
        self.gridline = mlines.Line2D(
            [], [],
            color=grid_color, alpha=grid_alpha, visible=gridOn,
            linestyle=grid_linestyle, linewidth=grid_linewidth, marker="",
            **grid_kw,
        )
        self.gridline.get_path()._interpolation_steps = \
            GRIDLINE_INTERPOLATION_STEPS
        self.label1 = mtext.Text(
            np.nan, np.nan,
            fontsize=labelsize, color=labelcolor, visible=label1On)
        self.label2 = mtext.Text(
            np.nan, np.nan,
            fontsize=labelsize, color=labelcolor, visible=label2On)
        for meth, attr in [("_get_tick1line", "tick1line"),
                           ("_get_tick2line", "tick2line"),
                           ("_get_gridline", "gridline"),
                           ("_get_text1", "label1"),
                           ("_get_text2", "label2")]:
            overridden_method = cbook._deprecate_method_override(
                getattr(__class__, meth), self, since="3.3", message="Relying "
                f"on {meth} to initialize Tick.{attr} is deprecated since "
                f"%(since)s and will not work %(removal)s; please directly "
                f"set the attribute in the subclass' __init__ instead.")
            if overridden_method:
                setattr(self, attr, overridden_method())
        for artist in [self.tick1line, self.tick2line, self.gridline,
                       self.label1, self.label2]:
            self._set_artist_props(artist)

        self.update_position(loc)

    @property
    @cbook.deprecated("3.1", alternative="Tick.label1", pending=True)
    def label(self):
        return self.label1

    def _set_labelrotation(self, labelrotation):
        if isinstance(labelrotation, str):
            mode = labelrotation
            angle = 0
        elif isinstance(labelrotation, (tuple, list)):
            mode, angle = labelrotation
        else:
            mode = 'default'
            angle = labelrotation
        cbook._check_in_list(['auto', 'default'], labelrotation=mode)
        self._labelrotation = (mode, angle)

    def apply_tickdir(self, tickdir):
        """Calculate ``self._pad`` and ``self._tickmarkers``."""

    def get_tickdir(self):
        return self._tickdir

    def get_tick_padding(self):
        """Get the length of the tick outside of the axes."""
        padding = {
            'in': 0.0,
            'inout': 0.5,
            'out': 1.0
        }
        return self._size * padding[self._tickdir]

    def get_children(self):
        children = [self.tick1line, self.tick2line,
                    self.gridline, self.label1, self.label2]
        return children

    def set_clip_path(self, clippath, transform=None):
        # docstring inherited
        martist.Artist.set_clip_path(self, clippath, transform)
        self.gridline.set_clip_path(clippath, transform)
        self.stale = True

    def get_pad_pixels(self):
        return self.figure.dpi * self._base_pad / 72

    def contains(self, mouseevent):
        """
        Test whether the mouse event occurred in the Tick marks.

        This function always returns false.  It is more useful to test if the
        axis as a whole contains the mouse rather than the set of tick marks.
        """
        inside, info = self._default_contains(mouseevent)
        if inside is not None:
            return inside, info
        return False, {}

    def set_pad(self, val):
        """
        Set the tick label pad in points

        Parameters
        ----------
        val : float
        """
        self._apply_params(pad=val)
        self.stale = True

    def get_pad(self):
        """Get the value of the tick label pad in points."""
        return self._base_pad

    def _get_text1(self):
        """Get the default Text 1 instance."""

    def _get_text2(self):
        """Get the default Text 2 instance."""

    def _get_tick1line(self):
        """Get the default line2D instance for tick1."""

    def _get_tick2line(self):
        """Get the default line2D instance for tick2."""

    def _get_gridline(self):
        """Get the default grid Line2d instance for this tick."""

    def get_loc(self):
        """Return the tick location (data coords) as a scalar."""
        return self._loc

    @martist.allow_rasterization
    def draw(self, renderer):
        if not self.get_visible():
            self.stale = False
            return
        renderer.open_group(self.__name__, gid=self.get_gid())
        for artist in [self.gridline, self.tick1line, self.tick2line,
                       self.label1, self.label2]:
            artist.draw(renderer)
        renderer.close_group(self.__name__)
        self.stale = False

    def set_label1(self, s):
        """
        Set the label1 text.

        Parameters
        ----------
        s : str
        """
        self.label1.set_text(s)
        self.stale = True

    set_label = set_label1

    def set_label2(self, s):
        """
        Set the label2 text.

        Parameters
        ----------
        s : str
        """
        self.label2.set_text(s)
        self.stale = True

    def set_url(self, url):
        """
        Set the url of label1 and label2.

        Parameters
        ----------
        url : str
        """
        super().set_url(url)
        self.label1.set_url(url)
        self.label2.set_url(url)
        self.stale = True

    def _set_artist_props(self, a):
        a.set_figure(self.figure)

    def get_view_interval(self):
        """
        Return the view limits ``(min, max)`` of the axis the tick belongs to.
        """
        raise NotImplementedError('Derived must override')

    def _apply_params(self, **kw):
        for name, target in [("gridOn", self.gridline),
                             ("tick1On", self.tick1line),
                             ("tick2On", self.tick2line),
                             ("label1On", self.label1),
                             ("label2On", self.label2)]:
            if name in kw:
                target.set_visible(kw.pop(name))
        if any(k in kw for k in ['size', 'width', 'pad', 'tickdir']):
            self._size = kw.pop('size', self._size)
            # Width could be handled outside this block, but it is
            # convenient to leave it here.
            self._width = kw.pop('width', self._width)
            self._base_pad = kw.pop('pad', self._base_pad)
            # apply_tickdir uses _size and _base_pad to make _pad,
            # and also makes _tickmarkers.
            self.apply_tickdir(kw.pop('tickdir', self._tickdir))
            self.tick1line.set_marker(self._tickmarkers[0])
            self.tick2line.set_marker(self._tickmarkers[1])
            for line in (self.tick1line, self.tick2line):
                line.set_markersize(self._size)
                line.set_markeredgewidth(self._width)
            # _get_text1_transform uses _pad from apply_tickdir.
            trans = self._get_text1_transform()[0]
            self.label1.set_transform(trans)
            trans = self._get_text2_transform()[0]
            self.label2.set_transform(trans)
        tick_kw = {k: v for k, v in kw.items() if k in ['color', 'zorder']}
        if 'color' in kw:
            tick_kw['markeredgecolor'] = kw['color']
        self.tick1line.set(**tick_kw)
        self.tick2line.set(**tick_kw)
        for k, v in tick_kw.items():
            setattr(self, '_' + k, v)

        if 'labelrotation' in kw:
            self._set_labelrotation(kw.pop('labelrotation'))
            self.label1.set(rotation=self._labelrotation[1])
            self.label2.set(rotation=self._labelrotation[1])

        label_kw = {k[5:]: v for k, v in kw.items()
                    if k in ['labelsize', 'labelcolor']}
        self.label1.set(**label_kw)
        self.label2.set(**label_kw)
        for k, v in label_kw.items():
            # for labelsize the text objects covert str ('small')
            # -> points. grab the integer from the `Text` object
            # instead of saving the string representation
            v = getattr(self.label1, 'get_' + k)()
            setattr(self, '_label' + k, v)

        grid_kw = {k[5:]: v for k, v in kw.items()
                   if k in _gridline_param_names}
        self.gridline.set(**grid_kw)
        for k, v in grid_kw.items():
            setattr(self, '_grid_' + k, v)

    def update_position(self, loc):
        """Set the location of tick in data coords with scalar *loc*."""
        raise NotImplementedError('Derived must override')

    def _get_text1_transform(self):
        raise NotImplementedError('Derived must override')

    def _get_text2_transform(self):
        raise NotImplementedError('Derived must override')


class XTick(Tick):
    """
    Contains all the Artists needed to make an x tick - the tick line,
    the label text and the grid line
    """
    __name__ = 'xtick'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # x in data coords, y in axes coords
        self.tick1line.set(
            xdata=[0], ydata=[0],
            transform=self.axes.get_xaxis_transform(which="tick1"),
            marker=self._tickmarkers[0],
        )
        self.tick2line.set(
            xdata=[0], ydata=[1],
            transform=self.axes.get_xaxis_transform(which="tick2"),
            marker=self._tickmarkers[1],
        )
        self.gridline.set(
            xdata=[0, 0], ydata=[0, 1],
            transform=self.axes.get_xaxis_transform(which="grid"),
        )
        # the y loc is 3 points below the min of y axis
        trans, va, ha = self._get_text1_transform()
        self.label1.set(
            x=0, y=0,
            verticalalignment=va, horizontalalignment=ha, transform=trans,
        )
        trans, va, ha = self._get_text2_transform()
        self.label2.set(
            x=0, y=1,
            verticalalignment=va, horizontalalignment=ha, transform=trans,
        )

    def _get_text1_transform(self):
        return self.axes.get_xaxis_text1_transform(self._pad)

    def _get_text2_transform(self):
        return self.axes.get_xaxis_text2_transform(self._pad)

    def apply_tickdir(self, tickdir):
        """Set tick direction. Valid values are 'in', 'out', 'inout'."""
        if tickdir is None:
            tickdir = mpl.rcParams['%s.direction' % self.__name__.lower()]
        cbook._check_in_list(['in', 'out', 'inout'], tickdir=tickdir)
        self._tickdir = tickdir

        if self._tickdir == 'in':
            self._tickmarkers = (mlines.TICKUP, mlines.TICKDOWN)
        elif self._tickdir == 'inout':
            self._tickmarkers = ('|', '|')
        else:
            self._tickmarkers = (mlines.TICKDOWN, mlines.TICKUP)
        self._pad = self._base_pad + self.get_tick_padding()
        self.stale = True

    def update_position(self, loc):
        """Set the location of tick in data coords with scalar *loc*."""
        self.tick1line.set_xdata((loc,))
        self.tick2line.set_xdata((loc,))
        self.gridline.set_xdata((loc,))
        self.label1.set_x(loc)
        self.label2.set_x(loc)
        self._loc = loc
        self.stale = True

    def get_view_interval(self):
        # docstring inherited
        return self.axes.viewLim.intervalx


class YTick(Tick):
    """
    Contains all the Artists needed to make a Y tick - the tick line,
    the label text and the grid line
    """
    __name__ = 'ytick'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # x in axes coords, y in data coords
        self.tick1line.set(
            xdata=[0], ydata=[0],
            transform=self.axes.get_yaxis_transform(which="tick1"),
            marker=self._tickmarkers[0],
        )
        self.tick2line.set(
            xdata=[1], ydata=[0],
            transform=self.axes.get_yaxis_transform(which="tick2"),
            marker=self._tickmarkers[1],
        )
        self.gridline.set(
            xdata=[0, 1], ydata=[0, 0],
            transform=self.axes.get_yaxis_transform(which="grid"),
        )
        # the y loc is 3 points below the min of y axis
        trans, va, ha = self._get_text1_transform()
        self.label1.set(
            x=0, y=0,
            verticalalignment=va, horizontalalignment=ha, transform=trans,
        )
        trans, va, ha = self._get_text2_transform()
        self.label2.set(
            x=1, y=0,
            verticalalignment=va, horizontalalignment=ha, transform=trans,
        )

    def _get_text1_transform(self):
        return self.axes.get_yaxis_text1_transform(self._pad)

    def _get_text2_transform(self):
        return self.axes.get_yaxis_text2_transform(self._pad)

    def apply_tickdir(self, tickdir):
        if tickdir is None:
            tickdir = mpl.rcParams['%s.direction' % self.__name__.lower()]
        self._tickdir = tickdir

        if self._tickdir == 'in':
            self._tickmarkers = (mlines.TICKRIGHT, mlines.TICKLEFT)
        elif self._tickdir == 'inout':
            self._tickmarkers = ('_', '_')
        else:
            self._tickmarkers = (mlines.TICKLEFT, mlines.TICKRIGHT)
        self._pad = self._base_pad + self.get_tick_padding()
        self.stale = True

    def update_position(self, loc):
        """Set the location of tick in data coords with scalar *loc*."""
        self.tick1line.set_ydata((loc,))
        self.tick2line.set_ydata((loc,))
        self.gridline.set_ydata((loc,))
        self.label1.set_y(loc)
        self.label2.set_y(loc)
        self._loc = loc
        self.stale = True

    def get_view_interval(self):
        # docstring inherited
        return self.axes.viewLim.intervaly


class Ticker:
    """
    A container for the objects defining tick position and format.

    Attributes
    ----------
    locator : `matplotlib.ticker.Locator` subclass
        Determines the positions of the ticks.
    formatter : `matplotlib.ticker.Formatter` subclass
        Determines the format of the tick labels.
    """

    def __init__(self):
        self._locator = None
        self._formatter = None

    @property
    def locator(self):
        return self._locator

    @locator.setter
    def locator(self, locator):
        if not isinstance(locator, mticker.Locator):
            cbook.warn_deprecated(
                "3.2", message="Support for locators that do not subclass "
                "matplotlib.ticker.Locator is deprecated since %(since)s and "
                "support for them will be removed %(removal)s.")
        self._locator = locator

    @property
    def formatter(self):
        return self._formatter

    @formatter.setter
    def formatter(self, formatter):
        if not isinstance(formatter, mticker.Formatter):
            cbook.warn_deprecated(
                "3.2", message="Support for formatters that do not subclass "
                "matplotlib.ticker.Formatter is deprecated since %(since)s "
                "and support for them will be removed %(removal)s.")
        self._formatter = formatter


class _LazyTickList:
    """
    A descriptor for lazy instantiation of tick lists.

    See comment above definition of the ``majorTicks`` and ``minorTicks``
    attributes.
    """

    def __init__(self, major):
        self._major = major

    def __get__(self, instance, cls):
        if instance is None:
            return self
        else:
            # instance._get_tick() can itself try to access the majorTicks
            # attribute (e.g. in certain projection classes which override
            # e.g. get_xaxis_text1_transform).  In order to avoid infinite
            # recursion, first set the majorTicks on the instance to an empty
            # list, then create the tick and append it.
            if self._major:
                instance.majorTicks = []
                tick = instance._get_tick(major=True)
                instance.majorTicks.append(tick)
                return instance.majorTicks
            else:
                instance.minorTicks = []
                tick = instance._get_tick(major=False)
                instance.minorTicks.append(tick)
                return instance.minorTicks


class Axis(martist.Artist):
    """
    Base class for `.XAxis` and `.YAxis`.

    Attributes
    ----------
    isDefault_label : bool

    axes : `matplotlib.axes.Axes`
        The `~.axes.Axes` to which the Axis belongs.
    major : `matplotlib.axis.Ticker`
        Determines the major tick positions and their label format.
    minor : `matplotlib.axis.Ticker`
        Determines the minor tick positions and their label format.
    callbacks : `matplotlib.cbook.CallbackRegistry`

    label : `.Text`
        The axis label.
    labelpad : float
        The distance between the axis label and the tick labels.
        Defaults to :rc:`axes.labelpad` = 4.
    offsetText : `.Text`
        A `.Text` object containing the data offset of the ticks (if any).
    pickradius : float
        The acceptance radius for containment tests. See also `.Axis.contains`.
    majorTicks : list of `.Tick`
        The major ticks.
    minorTicks : list of `.Tick`
        The minor ticks.
    """
    OFFSETTEXTPAD = 3

    def __str__(self):
        return "{}({},{})".format(
            type(self).__name__, *self.axes.transAxes.transform((0, 0)))

    def __init__(self, axes, pickradius=15):
        """
        Parameters
        ----------
        axes : `matplotlib.axes.Axes`
            The `~.axes.Axes` to which the created Axis belongs.
        pickradius : float
            The acceptance radius for containment tests. See also
            `.Axis.contains`.
        """
        martist.Artist.__init__(self)
        self._remove_overlapping_locs = True

        self.set_figure(axes.figure)

        self.isDefault_label = True

        self.axes = axes
        self.major = Ticker()
        self.minor = Ticker()
        self.callbacks = cbook.CallbackRegistry()

        self._autolabelpos = True
        self._smart_bounds = False  # Deprecated in 3.2

        self.label = mtext.Text(
            np.nan, np.nan,
            fontsize=mpl.rcParams['axes.labelsize'],
            fontweight=mpl.rcParams['axes.labelweight'],
            color=mpl.rcParams['axes.labelcolor'],
        )
        self._set_artist_props(self.label)
        self.offsetText = mtext.Text(np.nan, np.nan)
        self._set_artist_props(self.offsetText)

        self.labelpad = mpl.rcParams['axes.labelpad']

        self.pickradius = pickradius

        # Initialize here for testing; later add API
        self._major_tick_kw = dict()
        self._minor_tick_kw = dict()

        self.cla()
        self._set_scale('linear')

    # During initialization, Axis objects often create ticks that are later
    # unused; this turns out to be a very slow step.  Instead, use a custom
    # descriptor to make the tick lists lazy and instantiate them as needed.
    majorTicks = _LazyTickList(major=True)
    minorTicks = _LazyTickList(major=False)

    def get_remove_overlapping_locs(self):
        return self._remove_overlapping_locs

    def set_remove_overlapping_locs(self, val):
        self._remove_overlapping_locs = bool(val)

    remove_overlapping_locs = property(
        get_remove_overlapping_locs, set_remove_overlapping_locs,
        doc=('If minor ticker locations that overlap with major '
             'ticker locations should be trimmed.'))

    def set_label_coords(self, x, y, transform=None):
        """
        Set the coordinates of the label.

        By default, the x coordinate of the y label and the y coordinate of the
        x label are determined by the tick label bounding boxes, but this can
        lead to poor alignment of multiple labels if there are multiple axes.

        You can also specify the coordinate system of the label with the
        transform.  If None, the default coordinate system will be the axes
        coordinate system: (0, 0) is bottom left, (0.5, 0.5) is center, etc.
        """
        self._autolabelpos = False
        if transform is None:
            transform = self.axes.transAxes

        self.label.set_transform(transform)
        self.label.set_position((x, y))
        self.stale = True

    def get_transform(self):
        return self._scale.get_transform()

    def get_scale(self):
        """Return this Axis' scale (as a str)."""
        return self._scale.name

    def _set_scale(self, value, **kwargs):
        self._scale = mscale.scale_factory(value, self, **kwargs)
        self._scale.set_default_locators_and_formatters(self)

        self.isDefault_majloc = True
        self.isDefault_minloc = True
        self.isDefault_majfmt = True
        self.isDefault_minfmt = True

    def limit_range_for_scale(self, vmin, vmax):
        return self._scale.limit_range_for_scale(vmin, vmax, self.get_minpos())

    def get_children(self):
        return [self.label, self.offsetText,
                *self.get_major_ticks(), *self.get_minor_ticks()]

    def cla(self):
        """Clear this axis."""

        self.label.set_text('')  # self.set_label_text would change isDefault_

        self._set_scale('linear')

        # Clear the callback registry for this axis, or it may "leak"
        self.callbacks = cbook.CallbackRegistry()

        # whether the grids are on
        self._gridOnMajor = (
                mpl.rcParams['axes.grid'] and
                mpl.rcParams['axes.grid.which'] in ('both', 'major'))
        self._gridOnMinor = (
                mpl.rcParams['axes.grid'] and
                mpl.rcParams['axes.grid.which'] in ('both', 'minor'))

        self.reset_ticks()

        self.converter = None
        self.units = None
        self.set_units(None)
        self.stale = True

    def reset_ticks(self):
        """
        Re-initialize the major and minor Tick lists.

        Each list starts with a single fresh Tick.
        """
        # Restore the lazy tick lists.
        try:
            del self.majorTicks
        except AttributeError:
            pass
        try:
            del self.minorTicks
        except AttributeError:
            pass
        try:
            self.set_clip_path(self.axes.patch)
        except AttributeError:
            pass

    def set_tick_params(self, which='major', reset=False, **kw):
        """
        Set appearance parameters for ticks, ticklabels, and gridlines.

        For documentation of keyword arguments, see
        :meth:`matplotlib.axes.Axes.tick_params`.
        """
        cbook._check_in_list(['major', 'minor', 'both'], which=which)
        kwtrans = self._translate_tick_kw(kw)

        # the kwargs are stored in self._major/minor_tick_kw so that any
        # future new ticks will automatically get them
        if reset:
            if which in ['major', 'both']:
                self._major_tick_kw.clear()
                self._major_tick_kw.update(kwtrans)
            if which in ['minor', 'both']:
                self._minor_tick_kw.clear()
                self._minor_tick_kw.update(kwtrans)
            self.reset_ticks()
        else:
            if which in ['major', 'both']:
                self._major_tick_kw.update(kwtrans)
                for tick in self.majorTicks:
                    tick._apply_params(**kwtrans)
            if which in ['minor', 'both']:
                self._minor_tick_kw.update(kwtrans)
                for tick in self.minorTicks:
                    tick._apply_params(**kwtrans)
            # labelOn and labelcolor also apply to the offset text.
            if 'label1On' in kwtrans or 'label2On' in kwtrans:
                self.offsetText.set_visible(
                    self._major_tick_kw.get('label1On', False)
                    or self._major_tick_kw.get('label2On', False))
            if 'labelcolor' in kwtrans:
                self.offsetText.set_color(kwtrans['labelcolor'])

        self.stale = True

    @staticmethod
    def _translate_tick_kw(kw):
        # The following lists may be moved to a more accessible location.
        kwkeys = ['size', 'width', 'color', 'tickdir', 'pad',
                  'labelsize', 'labelcolor', 'zorder', 'gridOn',
                  'tick1On', 'tick2On', 'label1On', 'label2On',
                  'length', 'direction', 'left', 'bottom', 'right', 'top',
                  'labelleft', 'labelbottom', 'labelright', 'labeltop',
                  'labelrotation'] + _gridline_param_names
        kwtrans = {}
        if 'length' in kw:
            kwtrans['size'] = kw.pop('length')
        if 'direction' in kw:
            kwtrans['tickdir'] = kw.pop('direction')
        if 'rotation' in kw:
            kwtrans['labelrotation'] = kw.pop('rotation')
        if 'left' in kw:
            kwtrans['tick1On'] = kw.pop('left')
        if 'bottom' in kw:
            kwtrans['tick1On'] = kw.pop('bottom')
        if 'right' in kw:
            kwtrans['tick2On'] = kw.pop('right')
        if 'top' in kw:
            kwtrans['tick2On'] = kw.pop('top')
        if 'labelleft' in kw:
            kwtrans['label1On'] = kw.pop('labelleft')
        if 'labelbottom' in kw:
            kwtrans['label1On'] = kw.pop('labelbottom')
        if 'labelright' in kw:
            kwtrans['label2On'] = kw.pop('labelright')
        if 'labeltop' in kw:
            kwtrans['label2On'] = kw.pop('labeltop')
        if 'colors' in kw:
            c = kw.pop('colors')
            kwtrans['color'] = c
            kwtrans['labelcolor'] = c
        # Maybe move the checking up to the caller of this method.
        for key in kw:
            if key not in kwkeys:
                raise ValueError(
                    "keyword %s is not recognized; valid keywords are %s"
                    % (key, kwkeys))
        kwtrans.update(kw)
        return kwtrans

    def set_clip_path(self, clippath, transform=None):
        martist.Artist.set_clip_path(self, clippath, transform)
        for child in self.majorTicks + self.minorTicks:
            child.set_clip_path(clippath, transform)
        self.stale = True

    def get_view_interval(self):
        """Return the view limits ``(min, max)`` of this axis."""
        raise NotImplementedError('Derived must override')

    def set_view_interval(self, vmin, vmax, ignore=False):
        """
        Set the axis view limits.  This method is for internal use; Matplotlib
        users should typically use e.g. `~.Axes.set_xlim` or `~.Axes.set_ylim`.

        If *ignore* is False (the default), this method will never reduce the
        preexisting view limits, only expand them if *vmin* or *vmax* are not
        within them.  Moreover, the order of *vmin* and *vmax* does not matter;
        the orientation of the axis will not change.

        If *ignore* is True, the view limits will be set exactly to ``(vmin,
        vmax)`` in that order.
        """
        raise NotImplementedError('Derived must override')

    def get_data_interval(self):
        """Return the Interval instance for this axis data limits."""
        raise NotImplementedError('Derived must override')

    def set_data_interval(self, vmin, vmax, ignore=False):
        """
        Set the axis data limits.  This method is for internal use.

        If *ignore* is False (the default), this method will never reduce the
        preexisting data limits, only expand them if *vmin* or *vmax* are not
        within them.  Moreover, the order of *vmin* and *vmax* does not matter;
        the orientation of the axis will not change.

        If *ignore* is True, the data limits will be set exactly to ``(vmin,
        vmax)`` in that order.
        """
        raise NotImplementedError('Derived must override')

    def get_inverted(self):
        """
        Return whether this Axis is oriented in the "inverse" direction.

        The "normal" direction is increasing to the right for the x-axis and to
        the top for the y-axis; the "inverse" direction is increasing to the
        left for the x-axis and to the bottom for the y-axis.
        """
        low, high = self.get_view_interval()
        return high < low

    def set_inverted(self, inverted):
        """
        Set whether this Axis is oriented in the "inverse" direction.

        The "normal" direction is increasing to the right for the x-axis and to
        the top for the y-axis; the "inverse" direction is increasing to the
        left for the x-axis and to the bottom for the y-axis.
        """
        # Currently, must be implemented in subclasses using set_xlim/set_ylim
        # rather than generically using set_view_interval, so that shared
        # axes get updated as well.
        raise NotImplementedError('Derived must override')

    def set_default_intervals(self):
        """
        Set the default limits for the axis data and view interval if they
        have not been not mutated yet.
        """
        # this is mainly in support of custom object plotting.  For
        # example, if someone passes in a datetime object, we do not
        # know automagically how to set the default min/max of the
        # data and view limits.  The unit conversion AxisInfo
        # interface provides a hook for custom types to register
        # default limits through the AxisInfo.default_limits
        # attribute, and the derived code below will check for that
        # and use it if is available (else just use 0..1)

    def _set_artist_props(self, a):
        if a is None:
            return
        a.set_figure(self.figure)

    def get_ticklabel_extents(self, renderer):
        """
        Get the extents of the tick labels on either side
        of the axes.
        """

        ticks_to_draw = self._update_ticks()
        ticklabelBoxes, ticklabelBoxes2 = self._get_tick_bboxes(ticks_to_draw,
                                                                renderer)

        if len(ticklabelBoxes):
            bbox = mtransforms.Bbox.union(ticklabelBoxes)
        else:
            bbox = mtransforms.Bbox.from_extents(0, 0, 0, 0)
        if len(ticklabelBoxes2):
            bbox2 = mtransforms.Bbox.union(ticklabelBoxes2)
        else:
            bbox2 = mtransforms.Bbox.from_extents(0, 0, 0, 0)
        return bbox, bbox2

    @cbook.deprecated("3.2")
    def set_smart_bounds(self, value):
        """Set the axis to have smart bounds."""
        self._smart_bounds = value
        self.stale = True

    @cbook.deprecated("3.2")
    def get_smart_bounds(self):
        """Return whether the axis has smart bounds."""
        return self._smart_bounds

    def _update_ticks(self):
        """
        Update ticks (position and labels) using the current data interval of
        the axes.  Return the list of ticks that will be drawn.
        """
        major_locs = self.get_majorticklocs()
        major_labels = self.major.formatter.format_ticks(major_locs)
        major_ticks = self.get_major_ticks(len(major_locs))
        self.major.formatter.set_locs(major_locs)
        for tick, loc, label in zip(major_ticks, major_locs, major_labels):
            tick.update_position(loc)
            tick.set_label1(label)
            tick.set_label2(label)
        minor_locs = self.get_minorticklocs()
        minor_labels = self.minor.formatter.format_ticks(minor_locs)
        minor_ticks = self.get_minor_ticks(len(minor_locs))
        self.minor.formatter.set_locs(minor_locs)
        for tick, loc, label in zip(minor_ticks, minor_locs, minor_labels):
            tick.update_position(loc)
            tick.set_label1(label)
            tick.set_label2(label)
        ticks = [*major_ticks, *minor_ticks]

        view_low, view_high = self.get_view_interval()
        if view_low > view_high:
            view_low, view_high = view_high, view_low

        if self._smart_bounds and ticks:  # _smart_bounds is deprecated in 3.2
            # handle inverted limits
            data_low, data_high = sorted(self.get_data_interval())
            locs = np.sort([tick.get_loc() for tick in ticks])
            if data_low <= view_low:
                # data extends beyond view, take view as limit
                ilow = view_low
            else:
                # data stops within view, take best tick
                good_locs = locs[locs <= data_low]
                if len(good_locs):
                    # last tick prior or equal to first data point
                    ilow = good_locs[-1]
                else:
                    # No ticks (why not?), take first tick
                    ilow = locs[0]
            if data_high >= view_high:
                # data extends beyond view, take view as limit
                ihigh = view_high
            else:
                # data stops within view, take best tick
                good_locs = locs[locs >= data_high]
                if len(good_locs):
                    # first tick after or equal to last data point
                    ihigh = good_locs[0]
                else:
                    # No ticks (why not?), take last tick
                    ihigh = locs[-1]
            ticks = [tick for tick in ticks if ilow <= tick.get_loc() <= ihigh]

        interval_t = self.get_transform().transform([view_low, view_high])

        ticks_to_draw = []
        for tick in ticks:
            try:
                loc_t = self.get_transform().transform(tick.get_loc())
            except AssertionError:
                # transforms.transform doesn't allow masked values but
                # some scales might make them, so we need this try/except.
                pass
            else:
                if mtransforms._interval_contains_close(interval_t, loc_t):
                    ticks_to_draw.append(tick)

        return ticks_to_draw

    def _get_tick_bboxes(self, ticks, renderer):
        """Return lists of bboxes for ticks' label1's and label2's."""
        return ([tick.label1.get_window_extent(renderer)
                 for tick in ticks if tick.label1.get_visible()],
                [tick.label2.get_window_extent(renderer)
                 for tick in ticks if tick.label2.get_visible()])

    def get_tightbbox(self, renderer, *, for_layout_only=False):
        """
        Return a bounding box that encloses the axis. It only accounts
        tick labels, axis label, and offsetText.

        If *for_layout_only* is True, then the width of the label (if this
        is an x-axis) or the height of the label (if this is a y-axis) is
        collapsed to near zero.  This allows tight/constrained_layout to ignore
        too-long labels when doing their layout.
        """
        if not self.get_visible():
            return

        ticks_to_draw = self._update_ticks()

        self._update_label_position(renderer)

        # go back to just this axis's tick labels
        ticklabelBoxes, ticklabelBoxes2 = self._get_tick_bboxes(
                    ticks_to_draw, renderer)

        self._update_offset_text_position(ticklabelBoxes, ticklabelBoxes2)
        self.offsetText.set_text(self.major.formatter.get_offset())

        bboxes = [
            *(a.get_window_extent(renderer)
              for a in [self.offsetText]
              if a.get_visible()),
            *ticklabelBoxes,
            *ticklabelBoxes2,
        ]
        # take care of label
        if self.label.get_visible():
            bb = self.label.get_window_extent(renderer)
            # for constrained/tight_layout, we want to ignore the label's
            # width/height because the adjustments they make can't be improved.
            # this code collapses the relevant direction
            if for_layout_only:
                if self.axis_name == "x" and bb.width > 0:
                    bb.x0 = (bb.x0 + bb.x1) / 2 - 0.5
                    bb.x1 = bb.x0 + 1.0
                if self.axis_name == "y" and bb.height > 0:
                    bb.y0 = (bb.y0 + bb.y1) / 2 - 0.5
                    bb.y1 = bb.y0 + 1.0
            bboxes.append(bb)
        bboxes = [b for b in bboxes
                  if 0 < b.width < np.inf and 0 < b.height < np.inf]
        if bboxes:
            return mtransforms.Bbox.union(bboxes)
        else:
            return None

    def get_tick_padding(self):
        values = []
        if len(self.majorTicks):
            values.append(self.majorTicks[0].get_tick_padding())
        if len(self.minorTicks):
            values.append(self.minorTicks[0].get_tick_padding())
        return max(values, default=0)

    @martist.allow_rasterization
    def draw(self, renderer, *args, **kwargs):
        # docstring inherited

        if not self.get_visible():
            return
        renderer.open_group(__name__, gid=self.get_gid())

        ticks_to_draw = self._update_ticks()
        ticklabelBoxes, ticklabelBoxes2 = self._get_tick_bboxes(ticks_to_draw,
                                                                renderer)

        for tick in ticks_to_draw:
            tick.draw(renderer)

        # scale up the axis label box to also find the neighbors, not
        # just the tick labels that actually overlap note we need a
        # *copy* of the axis label box because we don't want to scale
        # the actual bbox

        self._update_label_position(renderer)

        self.label.draw(renderer)

        self._update_offset_text_position(ticklabelBoxes, ticklabelBoxes2)
        self.offsetText.set_text(self.major.formatter.get_offset())
        self.offsetText.draw(renderer)

        renderer.close_group(__name__)
        self.stale = False

    def get_gridlines(self):
        r"""Return this Axis' grid lines as a list of `.Line2D`\s."""
        ticks = self.get_major_ticks()
        return cbook.silent_list('Line2D gridline',
                                 [tick.gridline for tick in ticks])

    def get_label(self):
        """Return the axis label as a Text instance."""
        return self.label

    def get_offset_text(self):
        """Return the axis offsetText as a Text instance."""
        return self.offsetText

    def get_pickradius(self):
        """Return the depth of the axis used by the picker."""
        return self.pickradius

    def get_majorticklabels(self):
        """Return this Axis' major tick labels, as a list of `~.text.Text`."""
        ticks = self.get_major_ticks()
        labels1 = [tick.label1 for tick in ticks if tick.label1.get_visible()]
        labels2 = [tick.label2 for tick in ticks if tick.label2.get_visible()]
        return labels1 + labels2

    def get_minorticklabels(self):
        """Return this Axis' minor tick labels, as a list of `~.text.Text`."""
        ticks = self.get_minor_ticks()
        labels1 = [tick.label1 for tick in ticks if tick.label1.get_visible()]
        labels2 = [tick.label2 for tick in ticks if tick.label2.get_visible()]
        return labels1 + labels2

    def get_ticklabels(self, minor=False, which=None):
        """
        Get this Axis' tick labels.

        Parameters
        ----------
        minor : bool
           Whether to return the minor or the major ticklabels.

        which : None, ('minor', 'major', 'both')
           Overrides *minor*.

           Selects which ticklabels to return

        Returns
        -------
        list of `~matplotlib.text.Text`

        Notes
        -----
        The tick label strings are not populated until a ``draw`` method has
        been called.

        See also: `~.pyplot.draw` and `~.FigureCanvasBase.draw`.
        """
        if which is not None:
            if which == 'minor':
                return self.get_minorticklabels()
            elif which == 'major':
                return self.get_majorticklabels()
            elif which == 'both':
                return self.get_majorticklabels() + self.get_minorticklabels()
            else:
                cbook._check_in_list(['major', 'minor', 'both'], which=which)
        if minor:
            return self.get_minorticklabels()
        return self.get_majorticklabels()

    def get_majorticklines(self):
        r"""Return this Axis' major tick lines as a list of `.Line2D`\s."""
        lines = []
        ticks = self.get_major_ticks()
        for tick in ticks:
            lines.append(tick.tick1line)
            lines.append(tick.tick2line)
        return cbook.silent_list('Line2D ticklines', lines)

    def get_minorticklines(self):
        r"""Return this Axis' minor tick lines as a list of `.Line2D`\s."""
        lines = []
        ticks = self.get_minor_ticks()
        for tick in ticks:
            lines.append(tick.tick1line)
            lines.append(tick.tick2line)
        return cbook.silent_list('Line2D ticklines', lines)

    def get_ticklines(self, minor=False):
        r"""Return this Axis' tick lines as a list of `.Line2D`\s."""
        if minor:
            return self.get_minorticklines()
        return self.get_majorticklines()

    def get_majorticklocs(self):
        """Return this Axis' major tick locations in data coordinates."""
        return self.major.locator()

    def get_minorticklocs(self):
        """Return this Axis' minor tick locations in data coordinates."""
        # Remove minor ticks duplicating major ticks.
        major_locs = self.major.locator()
        minor_locs = self.minor.locator()
        transform = self._scale.get_transform()
        tr_minor_locs = transform.transform(minor_locs)
        tr_major_locs = transform.transform(major_locs)
        lo, hi = sorted(transform.transform(self.get_view_interval()))
        # Use the transformed view limits as scale.  1e-5 is the default rtol
        # for np.isclose.
        tol = (hi - lo) * 1e-5
        if self.remove_overlapping_locs:
            minor_locs = [
                loc for loc, tr_loc in zip(minor_locs, tr_minor_locs)
                if ~np.isclose(tr_loc, tr_major_locs, atol=tol, rtol=0).any()]
        return minor_locs

    @cbook._make_keyword_only("3.3", "minor")
    def get_ticklocs(self, minor=False):
        """Return this Axis' tick locations in data coordinates."""
        return self.get_minorticklocs() if minor else self.get_majorticklocs()

    def get_ticks_direction(self, minor=False):
        """
        Get the tick directions as a numpy array

        Parameters
        ----------
        minor : bool, default: False
            True to return the minor tick directions,
            False to return the major tick directions.

        Returns
        -------
        numpy array of tick directions
        """
        if minor:
            return np.array(
                [tick._tickdir for tick in self.get_minor_ticks()])
        else:
            return np.array(
                [tick._tickdir for tick in self.get_major_ticks()])

    def _get_tick(self, major):
        """Return the default tick instance."""
        raise NotImplementedError('derived must override')

    def _get_tick_label_size(self, axis_name):
        """
        Return the text size of tick labels for this Axis.

        This is a convenience function to avoid having to create a `Tick` in
        `.get_tick_space`, since it is expensive.
        """
        tick_kw = self._major_tick_kw
        size = tick_kw.get('labelsize',
                           mpl.rcParams[f'{axis_name}tick.labelsize'])
        return mtext.FontProperties(size=size).get_size_in_points()

    def _copy_tick_props(self, src, dest):
        """Copy the properties from *src* tick to *dest* tick."""
        if src is None or dest is None:
            return
        dest.label1.update_from(src.label1)
        dest.label2.update_from(src.label2)
        dest.tick1line.update_from(src.tick1line)
        dest.tick2line.update_from(src.tick2line)
        dest.gridline.update_from(src.gridline)

    def get_label_text(self):
        """Get the text of the label."""
        return self.label.get_text()

    def get_major_locator(self):
        """Get the locator of the major ticker."""
        return self.major.locator

    def get_minor_locator(self):
        """Get the locator of the minor ticker."""
        return self.minor.locator

    def get_major_formatter(self):
        """Get the formatter of the major ticker."""
        return self.major.formatter

    def get_minor_formatter(self):
        """Get the formatter of the minor ticker."""
        return self.minor.formatter

    def get_major_ticks(self, numticks=None):
        r"""Return the list of major `.Tick`\s."""
        if numticks is None:
            numticks = len(self.get_majorticklocs())

        while len(self.majorTicks) < numticks:
            # Update the new tick label properties from the old.
            tick = self._get_tick(major=True)
            self.majorTicks.append(tick)
            tick.gridline.set_visible(self._gridOnMajor)
            self._copy_tick_props(self.majorTicks[0], tick)

        return self.majorTicks[:numticks]

    def get_minor_ticks(self, numticks=None):
        r"""Return the list of minor `.Tick`\s."""
        if numticks is None:
            numticks = len(self.get_minorticklocs())

        while len(self.minorTicks) < numticks:
            # Update the new tick label properties from the old.
            tick = self._get_tick(major=False)
            self.minorTicks.append(tick)
            tick.gridline.set_visible(self._gridOnMinor)
            self._copy_tick_props(self.minorTicks[0], tick)

        return self.minorTicks[:numticks]

    def grid(self, b=None, which='major', **kwargs):
        """
        Configure the grid lines.

        Parameters
        ----------
        b : bool or None
            Whether to show the grid lines. If any *kwargs* are supplied,
            it is assumed you want the grid on and *b* will be set to True.

            If *b* is *None* and there are no *kwargs*, this toggles the
            visibility of the lines.

        which : {'major', 'minor', 'both'}
            The grid lines to apply the changes on.

        **kwargs : `.Line2D` properties
            Define the line properties of the grid, e.g.::

                grid(color='r', linestyle='-', linewidth=2)

        """
        if len(kwargs):
            if not b and b is not None:  # something false-like but not None
                cbook._warn_external('First parameter to grid() is false, '
                                     'but line properties are supplied. The '
                                     'grid will be enabled.')
            b = True
        which = which.lower()
        cbook._check_in_list(['major', 'minor', 'both'], which=which)
        gridkw = {'grid_' + item[0]: item[1] for item in kwargs.items()}

        if which in ['minor', 'both']:
            if b is None:
                self._gridOnMinor = not self._gridOnMinor
            else:
                self._gridOnMinor = b
            self.set_tick_params(which='minor', gridOn=self._gridOnMinor,
                                 **gridkw)
        if which in ['major', 'both']:
            if b is None:
                self._gridOnMajor = not self._gridOnMajor
            else:
                self._gridOnMajor = b
            self.set_tick_params(which='major', gridOn=self._gridOnMajor,
                                 **gridkw)
        self.stale = True

    def update_units(self, data):
        """
        Introspect *data* for units converter and update the
        axis.converter instance if necessary. Return *True*
        if *data* is registered for unit conversion.
        """
        converter = munits.registry.get_converter(data)
        if converter is None:
            return False

        neednew = self.converter != converter
        self.converter = converter
        default = self.converter.default_units(data, self)
        if default is not None and self.units is None:
            self.set_units(default)

        if neednew:
            self._update_axisinfo()
        self.stale = True
        return True

    def _update_axisinfo(self):
        """
        Check the axis converter for the stored units to see if the
        axis info needs to be updated.
        """
        if self.converter is None:
            return

        info = self.converter.axisinfo(self.units, self)

        if info is None:
            return
        if info.majloc is not None and \
           self.major.locator != info.majloc and self.isDefault_majloc:
            self.set_major_locator(info.majloc)
            self.isDefault_majloc = True
        if info.minloc is not None and \
           self.minor.locator != info.minloc and self.isDefault_minloc:
            self.set_minor_locator(info.minloc)
            self.isDefault_minloc = True
        if info.majfmt is not None and \
           self.major.formatter != info.majfmt and self.isDefault_majfmt:
            self.set_major_formatter(info.majfmt)
            self.isDefault_majfmt = True
        if info.minfmt is not None and \
           self.minor.formatter != info.minfmt and self.isDefault_minfmt:
            self.set_minor_formatter(info.minfmt)
            self.isDefault_minfmt = True
        if info.label is not None and self.isDefault_label:
            self.set_label_text(info.label)
            self.isDefault_label = True

        self.set_default_intervals()

    def have_units(self):
        return self.converter is not None or self.units is not None

    def convert_units(self, x):
        # If x is natively supported by Matplotlib, doesn't need converting
        if munits._is_natively_supported(x):
            return x

        if self.converter is None:
            self.converter = munits.registry.get_converter(x)

        if self.converter is None:
            return x
        try:
            ret = self.converter.convert(x, self.units, self)
        except Exception as e:
            raise munits.ConversionError('Failed to convert value(s) to axis '
                                         f'units: {x!r}') from e
        return ret

    def set_units(self, u):
        """
        Set the units for axis.

        Parameters
        ----------
        u : units tag
        """
        if u == self.units:
            return
        self.units = u
        self._update_axisinfo()
        self.callbacks.process('units')
        self.callbacks.process('units finalize')
        self.stale = True

    def get_units(self):
        """Return the units for axis."""
        return self.units

    def set_label_text(self, label, fontdict=None, **kwargs):
        """
        Set the text value of the axis label.

        Parameters
        ----------
        label : str
            Text string.
        fontdict : dict
            Text properties.
        **kwargs
            Merged into fontdict.
        """
        self.isDefault_label = False
        self.label.set_text(label)
        if fontdict is not None:
            self.label.update(fontdict)
        self.label.update(kwargs)
        self.stale = True
        return self.label

    def set_major_formatter(self, formatter):
        """
        Set the formatter of the major ticker.

        In addition to a `~matplotlib.ticker.Formatter` instance,
        this also accepts a ``str`` or function.

        For a ``str`` a `~matplotlib.ticker.StrMethodFormatter` is used.
        The field used for the value must be labeled ``'x'`` and the field used
        for the position must be labeled ``'pos'``.
        See the  `~matplotlib.ticker.StrMethodFormatter` documentation for
        more information.

        For a function, a `~matplotlib.ticker.FuncFormatter` is used.
        The function must take two inputs (a tick value ``x`` and a
        position ``pos``), and return a string containing the corresponding
        tick label.
        See the  `~matplotlib.ticker.FuncFormatter` documentation for
        more information.

        Parameters
        ----------
        formatter : `~matplotlib.ticker.Formatter`, ``str``, or function
        """
        self._set_formatter(formatter, self.major)

    def set_minor_formatter(self, formatter):
        """
        Set the formatter of the minor ticker.

        In addition to a `~matplotlib.ticker.Formatter` instance,
        this also accepts a ``str`` or function.
        See `.Axis.set_major_formatter` for more information.

        Parameters
        ----------
        formatter : `~matplotlib.ticker.Formatter`, ``str``, or function
        """
        self._set_formatter(formatter, self.minor)

    def _set_formatter(self, formatter, level):
        if isinstance(formatter, str):
            formatter = mticker.StrMethodFormatter(formatter)
        # Don't allow any other TickHelper to avoid easy-to-make errors,
        # like using a Locator instead of a Formatter.
        elif (callable(formatter) and
              not isinstance(formatter, mticker.TickHelper)):
            formatter = mticker.FuncFormatter(formatter)
        else:
            cbook._check_isinstance(mticker.Formatter, formatter=formatter)

        if (isinstance(formatter, mticker.FixedFormatter)
                and len(formatter.seq) > 0
                and not isinstance(level.locator, mticker.FixedLocator)):
            cbook._warn_external('FixedFormatter should only be used together '
                                 'with FixedLocator')

        if level == self.major:
            self.isDefault_majfmt = False
        else:
            self.isDefault_minfmt = False

        level.formatter = formatter
        formatter.set_axis(self)
        self.stale = True

    def set_major_locator(self, locator):
        """
        Set the locator of the major ticker.

        Parameters
        ----------
        locator : `~matplotlib.ticker.Locator`
        """
        cbook._check_isinstance(mticker.Locator, locator=locator)
        self.isDefault_majloc = False
        self.major.locator = locator
        if self.major.formatter:
            self.major.formatter._set_locator(locator)
        locator.set_axis(self)
        self.stale = True

    def set_minor_locator(self, locator):
        """
        Set the locator of the minor ticker.

        Parameters
        ----------
        locator : `~matplotlib.ticker.Locator`
        """
        cbook._check_isinstance(mticker.Locator, locator=locator)
        self.isDefault_minloc = False
        self.minor.locator = locator
        if self.minor.formatter:
            self.minor.formatter._set_locator(locator)
        locator.set_axis(self)
        self.stale = True

    def set_pickradius(self, pickradius):
        """
        Set the depth of the axis used by the picker.

        Parameters
        ----------
        pickradius :  float
        """
        self.pickradius = pickradius

    # Helper for set_ticklabels. Defining it here makes it pickleable.
    @staticmethod
    def _format_with_dict(tickd, x, pos):
        return tickd.get(x, "")

    def set_ticklabels(self, ticklabels, *, minor=False, **kwargs):
        r"""
        Set the text values of the tick labels.

        .. warning::
            This method should only be used after fixing the tick positions
            using `.Axis.set_ticks`. Otherwise, the labels may end up in
            unexpected positions.

        Parameters
        ----------
        ticklabels : sequence of str or of `.Text`\s
            Texts for labeling each tick location in the sequence set by
            `.Axis.set_ticks`; the number of labels must match the number of
            locations.
        minor : bool
            If True, set minor ticks instead of major ticks.
        **kwargs
            Text properties.

        Returns
        -------
        list of `.Text`\s
            For each tick, includes ``tick.label1`` if it is visible, then
            ``tick.label2`` if it is visible, in that order.
        """
        ticklabels = [t.get_text() if hasattr(t, 'get_text') else t
                      for t in ticklabels]
        locator = (self.get_minor_locator() if minor
                   else self.get_major_locator())
        if isinstance(locator, mticker.FixedLocator):
            # Passing [] as a list of ticklabels is often used as a way to
            # remove all tick labels, so only error for > 0 ticklabels
            if len(locator.locs) != len(ticklabels) and len(ticklabels) != 0:
                raise ValueError(
                    "The number of FixedLocator locations"
                    f" ({len(locator.locs)}), usually from a call to"
                    " set_ticks, does not match"
                    f" the number of ticklabels ({len(ticklabels)}).")
            tickd = {loc: lab for loc, lab in zip(locator.locs, ticklabels)}
            func = functools.partial(self._format_with_dict, tickd)
            formatter = mticker.FuncFormatter(func)
        else:
            formatter = mticker.FixedFormatter(ticklabels)

        if minor:
            self.set_minor_formatter(formatter)
            locs = self.get_minorticklocs()
            ticks = self.get_minor_ticks(len(locs))
        else:
            self.set_major_formatter(formatter)
            locs = self.get_majorticklocs()
            ticks = self.get_major_ticks(len(locs))

        ret = []
        for pos, (loc, tick) in enumerate(zip(locs, ticks)):
            tick.update_position(loc)
            tick_label = formatter(loc, pos)
            # deal with label1
            tick.label1.set_text(tick_label)
            tick.label1.update(kwargs)
            # deal with label2
            tick.label2.set_text(tick_label)
            tick.label2.update(kwargs)
            # only return visible tick labels
            if tick.label1.get_visible():
                ret.append(tick.label1)
            if tick.label2.get_visible():
                ret.append(tick.label2)

        self.stale = True
        return ret

    # Wrapper around set_ticklabels used to generate Axes.set_x/ytickabels; can
    # go away once the API of Axes.set_x/yticklabels becomes consistent.
    @cbook._make_keyword_only("3.3", "fontdict")
    def _set_ticklabels(self, labels, fontdict=None, minor=False, **kwargs):
        """
        Set this Axis' labels with list of string labels.

        .. warning::
            This method should only be used after fixing the tick positions
            using `.Axis.set_ticks`. Otherwise, the labels may end up in
            unexpected positions.

        Parameters
        ----------
        labels : list of str
            The label texts.

        fontdict : dict, optional
            A dictionary controlling the appearance of the ticklabels.
            The default *fontdict* is::

               {'fontsize': rcParams['axes.titlesize'],
                'fontweight': rcParams['axes.titleweight'],
                'verticalalignment': 'baseline',
                'horizontalalignment': loc}

        minor : bool, default: False
            Whether to set the minor ticklabels rather than the major ones.

        Returns
        -------
        list of `~.Text`
            The labels.

        Other Parameters
        ----------------
        **kwargs : `~.text.Text` properties.
        """
        if fontdict is not None:
            kwargs.update(fontdict)
        return self.set_ticklabels(labels, minor=minor, **kwargs)

    @cbook._make_keyword_only("3.2", "minor")
    def set_ticks(self, ticks, minor=False):
        """
        Set this Axis' tick locations.

        Parameters
        ----------
        ticks : list of floats
            List of tick locations.
        minor : bool, default: False
            If ``False``, set the major ticks; if ``True``, the minor ticks.
        """
        # XXX if the user changes units, the information will be lost here
        ticks = self.convert_units(ticks)
        if len(ticks) > 1:
            xleft, xright = self.get_view_interval()
            if xright > xleft:
                self.set_view_interval(min(ticks), max(ticks))
            else:
                self.set_view_interval(max(ticks), min(ticks))
        self.axes.stale = True
        if minor:
            self.set_minor_locator(mticker.FixedLocator(ticks))
            return self.get_minor_ticks(len(ticks))
        else:
            self.set_major_locator(mticker.FixedLocator(ticks))
            return self.get_major_ticks(len(ticks))

    def _get_tick_boxes_siblings(self, xdir, renderer):
        """
        Get the bounding boxes for this `.axis` and its siblings
        as set by `.Figure.align_xlabels` or  `.Figure.align_ylablels`.

        By default it just gets bboxes for self.
        """
        raise NotImplementedError('Derived must override')

    def _update_label_position(self, renderer):
        """
        Update the label position based on the bounding box enclosing
        all the ticklabels and axis spine.
        """
        raise NotImplementedError('Derived must override')

    def _update_offset_text_position(self, bboxes, bboxes2):
        """
        Update the offset text position based on the sequence of bounding
        boxes of all the ticklabels.
        """
        raise NotImplementedError('Derived must override')

    @cbook.deprecated("3.3")
    def pan(self, numsteps):
        """Pan by *numsteps* (can be positive or negative)."""
        self.major.locator.pan(numsteps)

    @cbook.deprecated("3.3")
    def zoom(self, direction):
        """Zoom in/out on axis; if *direction* is >0 zoom in, else zoom out."""
        self.major.locator.zoom(direction)

    def axis_date(self, tz=None):
        """
        Sets up axis ticks and labels to treat data along this Axis as dates.

        Parameters
        ----------
        tz : str or `datetime.tzinfo`, default: :rc:`timezone`
            The timezone used to create date labels.
        """
        # By providing a sample datetime instance with the desired timezone,
        # the registered converter can be selected, and the "units" attribute,
        # which is the timezone, can be set.
        if isinstance(tz, str):
            import dateutil.tz
            tz = dateutil.tz.gettz(tz)
        self.update_units(datetime.datetime(2009, 1, 1, 0, 0, 0, 0, tz))

    def get_tick_space(self):
        """Return the estimated number of ticks that can fit on the axis."""
        # Must be overridden in the subclass
        raise NotImplementedError()

    def _get_ticks_position(self):
        """
        Helper for `XAxis.get_ticks_position` and `YAxis.get_ticks_position`.

        Check the visibility of tick1line, label1, tick2line, and label2 on
        the first major and the first minor ticks, and return

        - 1 if only tick1line and label1 are visible (which corresponds to
          "bottom" for the x-axis and "left" for the y-axis);
        - 2 if only tick2line and label2 are visible (which corresponds to
          "top" for the x-axis and "right" for the y-axis);
        - "default" if only tick1line, tick2line and label1 are visible;
        - "unknown" otherwise.
        """
        major = self.majorTicks[0]
        minor = self.minorTicks[0]
        if all(tick.tick1line.get_visible()
               and not tick.tick2line.get_visible()
               and tick.label1.get_visible()
               and not tick.label2.get_visible()
               for tick in [major, minor]):
            return 1
        elif all(tick.tick2line.get_visible()
                 and not tick.tick1line.get_visible()
                 and tick.label2.get_visible()
                 and not tick.label1.get_visible()
                 for tick in [major, minor]):
            return 2
        elif all(tick.tick1line.get_visible()
                 and tick.tick2line.get_visible()
                 and tick.label1.get_visible()
                 and not tick.label2.get_visible()
                 for tick in [major, minor]):
            return "default"
        else:
            return "unknown"

    def get_label_position(self):
        """
        Return the label position (top or bottom)
        """
        return self.label_position

    def set_label_position(self, position):
        """
        Set the label position (top or bottom)

        Parameters
        ----------
        position : {'top', 'bottom'}
        """
        raise NotImplementedError()

    def get_minpos(self):
        raise NotImplementedError()


def _make_getset_interval(method_name, lim_name, attr_name):
    """
    Helper to generate ``get_{data,view}_interval`` and
    ``set_{data,view}_interval`` implementations.
    """

    def getter(self):
        # docstring inherited.
        return getattr(getattr(self.axes, lim_name), attr_name)

    def setter(self, vmin, vmax, ignore=False):
        # docstring inherited.
        if ignore:
            setattr(getattr(self.axes, lim_name), attr_name, (vmin, vmax))
        else:
            oldmin, oldmax = getter(self)
            if oldmin < oldmax:
                setter(self, min(vmin, vmax, oldmin), max(vmin, vmax, oldmax),
                       ignore=True)
            else:
                setter(self, max(vmin, vmax, oldmin), min(vmin, vmax, oldmax),
                       ignore=True)
        self.stale = True

    getter.__name__ = f"get_{method_name}_interval"
    setter.__name__ = f"set_{method_name}_interval"

    return getter, setter


class XAxis(Axis):
    __name__ = 'xaxis'
    axis_name = 'x'  #: Read-only name identifying the axis.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # x in axes coords, y in display coords (to be updated at draw time by
        # _update_label_positions and _update_offset_text_position).
        self.label.set(
            x=0.5, y=0,
            verticalalignment='top', horizontalalignment='center',
            transform=mtransforms.blended_transform_factory(
                self.axes.transAxes, mtransforms.IdentityTransform()),
        )
        self.label_position = 'bottom'
        self.offsetText.set(
            x=1, y=0,
            verticalalignment='top', horizontalalignment='right',
            transform=mtransforms.blended_transform_factory(
                self.axes.transAxes, mtransforms.IdentityTransform()),
            fontsize=mpl.rcParams['xtick.labelsize'],
            color=mpl.rcParams['xtick.color'],
        )
        self.offset_text_position = 'bottom'

    def contains(self, mouseevent):
        """Test whether the mouse event occurred in the x axis."""
        inside, info = self._default_contains(mouseevent)
        if inside is not None:
            return inside, info

        x, y = mouseevent.x, mouseevent.y
        try:
            trans = self.axes.transAxes.inverted()
            xaxes, yaxes = trans.transform((x, y))
        except ValueError:
            return False, {}
        (l, b), (r, t) = self.axes.transAxes.transform([(0, 0), (1, 1)])
        inaxis = 0 <= xaxes <= 1 and (
            b - self.pickradius < y < b or
            t < y < t + self.pickradius)
        return inaxis, {}

    def _get_tick(self, major):
        if major:
            tick_kw = self._major_tick_kw
        else:
            tick_kw = self._minor_tick_kw
        return XTick(self.axes, 0, major=major, **tick_kw)

    def set_label_position(self, position):
        """
        Set the label position (top or bottom)

        Parameters
        ----------
        position : {'top', 'bottom'}
        """
        self.label.set_verticalalignment(cbook._check_getitem({
            'top': 'baseline', 'bottom': 'top',
        }, position=position))
        self.label_position = position
        self.stale = True

    def _get_tick_boxes_siblings(self, renderer):
        """
        Get the bounding boxes for this `.axis` and its siblings
        as set by `.Figure.align_xlabels` or  `.Figure.align_ylablels`.

        By default it just gets bboxes for self.
        """
        bboxes = []
        bboxes2 = []
        # get the Grouper that keeps track of x-label groups for this figure
        grp = self.figure._align_xlabel_grp
        # if we want to align labels from other axes:
        for nn, axx in enumerate(grp.get_siblings(self.axes)):
            ticks_to_draw = axx.xaxis._update_ticks()
            tlb, tlb2 = axx.xaxis._get_tick_bboxes(ticks_to_draw, renderer)
            bboxes.extend(tlb)
            bboxes2.extend(tlb2)
        return bboxes, bboxes2

    def _update_label_position(self, renderer):
        """
        Update the label position based on the bounding box enclosing
        all the ticklabels and axis spine
        """
        if not self._autolabelpos:
            return

        # get bounding boxes for this axis and any siblings
        # that have been set by `fig.align_xlabels()`
        bboxes, bboxes2 = self._get_tick_boxes_siblings(renderer=renderer)

        x, y = self.label.get_position()
        if self.label_position == 'bottom':
            try:
                spine = self.axes.spines['bottom']
                spinebbox = spine.get_transform().transform_path(
                    spine.get_path()).get_extents()
            except KeyError:
                # use axes if spine doesn't exist
                spinebbox = self.axes.bbox
            bbox = mtransforms.Bbox.union(bboxes + [spinebbox])
            bottom = bbox.y0

            self.label.set_position(
                (x, bottom - self.labelpad * self.figure.dpi / 72)
            )

        else:
            try:
                spine = self.axes.spines['top']
                spinebbox = spine.get_transform().transform_path(
                    spine.get_path()).get_extents()
            except KeyError:
                # use axes if spine doesn't exist
                spinebbox = self.axes.bbox
            bbox = mtransforms.Bbox.union(bboxes2 + [spinebbox])
            top = bbox.y1

            self.label.set_position(
                (x, top + self.labelpad * self.figure.dpi / 72)
            )

    def _update_offset_text_position(self, bboxes, bboxes2):
        """
        Update the offset_text position based on the sequence of bounding
        boxes of all the ticklabels
        """
        x, y = self.offsetText.get_position()
        if not hasattr(self, '_tick_position'):
            self._tick_position = 'bottom'
        if self._tick_position == 'bottom':
            if not len(bboxes):
                bottom = self.axes.bbox.ymin
            else:
                bbox = mtransforms.Bbox.union(bboxes)
                bottom = bbox.y0
            y = bottom - self.OFFSETTEXTPAD * self.figure.dpi / 72
        else:
            if not len(bboxes2):
                top = self.axes.bbox.ymax
            else:
                bbox = mtransforms.Bbox.union(bboxes2)
                top = bbox.y1
            y = top + self.OFFSETTEXTPAD * self.figure.dpi / 72
        self.offsetText.set_position((x, y))

    def get_text_heights(self, renderer):
        """
        Return how much space should be reserved for text above and below the
        axes, as a pair of floats.
        """
        bbox, bbox2 = self.get_ticklabel_extents(renderer)
        # MGDTODO: Need a better way to get the pad
        padPixels = self.majorTicks[0].get_pad_pixels()

        above = 0.0
        if bbox2.height:
            above += bbox2.height + padPixels
        below = 0.0
        if bbox.height:
            below += bbox.height + padPixels

        if self.get_label_position() == 'top':
            above += self.label.get_window_extent(renderer).height + padPixels
        else:
            below += self.label.get_window_extent(renderer).height + padPixels
        return above, below

    def set_ticks_position(self, position):
        """
        Set the ticks position.

        Parameters
        ----------
        position : {'top', 'bottom', 'both', 'default', 'none'}
            'both' sets the ticks to appear on both positions, but does not
            change the tick labels.  'default' resets the tick positions to
            the default: ticks on both positions, labels at bottom.  'none'
            can be used if you don't want any ticks. 'none' and 'both'
            affect only the ticks, not the labels.
        """
        cbook._check_in_list(['top', 'bottom', 'both', 'default', 'none'],
                             position=position)
        if position == 'top':
            self.set_tick_params(which='both', top=True, labeltop=True,
                                 bottom=False, labelbottom=False)
            self._tick_position = 'top'
            self.offsetText.set_verticalalignment('bottom')
        elif position == 'bottom':
            self.set_tick_params(which='both', top=False, labeltop=False,
                                 bottom=True, labelbottom=True)
            self._tick_position = 'bottom'
            self.offsetText.set_verticalalignment('top')
        elif position == 'both':
            self.set_tick_params(which='both', top=True,
                                 bottom=True)
        elif position == 'none':
            self.set_tick_params(which='both', top=False,
                                 bottom=False)
        elif position == 'default':
            self.set_tick_params(which='both', top=True, labeltop=False,
                                 bottom=True, labelbottom=True)
            self._tick_position = 'bottom'
            self.offsetText.set_verticalalignment('top')
        else:
            assert False, "unhandled parameter not caught by _check_in_list"
        self.stale = True

    def tick_top(self):
        """
        Move ticks and ticklabels (if present) to the top of the axes.
        """
        label = True
        if 'label1On' in self._major_tick_kw:
            label = (self._major_tick_kw['label1On']
                     or self._major_tick_kw['label2On'])
        self.set_ticks_position('top')
        # If labels were turned off before this was called, leave them off.
        self.set_tick_params(which='both', labeltop=label)

    def tick_bottom(self):
        """
        Move ticks and ticklabels (if present) to the bottom of the axes.
        """
        label = True
        if 'label1On' in self._major_tick_kw:
            label = (self._major_tick_kw['label1On']
                     or self._major_tick_kw['label2On'])
        self.set_ticks_position('bottom')
        # If labels were turned off before this was called, leave them off.
        self.set_tick_params(which='both', labelbottom=label)

    def get_ticks_position(self):
        """
        Return the ticks position ("top", "bottom", "default", or "unknown").
        """
        return {1: "bottom", 2: "top",
                "default": "default", "unknown": "unknown"}[
                    self._get_ticks_position()]

    get_view_interval, set_view_interval = _make_getset_interval(
        "view", "viewLim", "intervalx")
    get_data_interval, set_data_interval = _make_getset_interval(
        "data", "dataLim", "intervalx")

    def get_minpos(self):
        return self.axes.dataLim.minposx

    def set_inverted(self, inverted):
        # docstring inherited
        a, b = self.get_view_interval()
        # cast to bool to avoid bad interaction between python 3.8 and np.bool_
        self.axes.set_xlim(sorted((a, b), reverse=bool(inverted)), auto=None)

    def set_default_intervals(self):
        # docstring inherited
        xmin, xmax = 0., 1.
        dataMutated = self.axes.dataLim.mutatedx()
        viewMutated = self.axes.viewLim.mutatedx()
        if not dataMutated or not viewMutated:
            if self.converter is not None:
                info = self.converter.axisinfo(self.units, self)
                if info.default_limits is not None:
                    valmin, valmax = info.default_limits
                    xmin = self.converter.convert(valmin, self.units, self)
                    xmax = self.converter.convert(valmax, self.units, self)
            if not dataMutated:
                self.axes.dataLim.intervalx = xmin, xmax
            if not viewMutated:
                self.axes.viewLim.intervalx = xmin, xmax
        self.stale = True

    def get_tick_space(self):
        ends = self.axes.transAxes.transform([[0, 0], [1, 0]])
        length = ((ends[1][0] - ends[0][0]) / self.axes.figure.dpi) * 72
        # There is a heuristic here that the aspect ratio of tick text
        # is no more than 3:1
        size = self._get_tick_label_size('x') * 3
        if size > 0:
            return int(np.floor(length / size))
        else:
            return 2**31 - 1


class YAxis(Axis):
    __name__ = 'yaxis'
    axis_name = 'y'  #: Read-only name identifying the axis.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # x in display coords, y in axes coords (to be updated at draw time by
        # _update_label_positions and _update_offset_text_position).
        self.label.set(
            x=0, y=0.5,
            verticalalignment='bottom', horizontalalignment='center',
            rotation='vertical', rotation_mode='anchor',
            transform=mtransforms.blended_transform_factory(
                mtransforms.IdentityTransform(), self.axes.transAxes),
        )
        self.label_position = 'left'
        # x in axes coords, y in display coords(!).
        self.offsetText.set(
            x=0, y=0.5,
            verticalalignment='baseline', horizontalalignment='left',
            transform=mtransforms.blended_transform_factory(
                self.axes.transAxes, mtransforms.IdentityTransform()),
            fontsize=mpl.rcParams['ytick.labelsize'],
            color=mpl.rcParams['ytick.color'],
        )
        self.offset_text_position = 'left'

    def contains(self, mouseevent):
        # docstring inherited
        inside, info = self._default_contains(mouseevent)
        if inside is not None:
            return inside, info

        x, y = mouseevent.x, mouseevent.y
        try:
            trans = self.axes.transAxes.inverted()
            xaxes, yaxes = trans.transform((x, y))
        except ValueError:
            return False, {}
        (l, b), (r, t) = self.axes.transAxes.transform([(0, 0), (1, 1)])
        inaxis = 0 <= yaxes <= 1 and (
            l - self.pickradius < x < l or
            r < x < r + self.pickradius)
        return inaxis, {}

    def _get_tick(self, major):
        if major:
            tick_kw = self._major_tick_kw
        else:
            tick_kw = self._minor_tick_kw
        return YTick(self.axes, 0, major=major, **tick_kw)

    def set_label_position(self, position):
        """
        Set the label position (left or right)

        Parameters
        ----------
        position : {'left', 'right'}
        """
        self.label.set_rotation_mode('anchor')
        self.label.set_horizontalalignment('center')
        self.label.set_verticalalignment(cbook._check_getitem({
            'left': 'bottom', 'right': 'top',
        }, position=position))
        self.label_position = position
        self.stale = True

    def _get_tick_boxes_siblings(self, renderer):
        """
        Get the bounding boxes for this `.axis` and its siblings
        as set by `.Figure.align_xlabels` or  `.Figure.align_ylablels`.

        By default it just gets bboxes for self.
        """
        bboxes = []
        bboxes2 = []
        # get the Grouper that keeps track of y-label groups for this figure
        grp = self.figure._align_ylabel_grp
        # if we want to align labels from other axes:
        for axx in grp.get_siblings(self.axes):
            ticks_to_draw = axx.yaxis._update_ticks()
            tlb, tlb2 = axx.yaxis._get_tick_bboxes(ticks_to_draw, renderer)
            bboxes.extend(tlb)
            bboxes2.extend(tlb2)
        return bboxes, bboxes2

    def _update_label_position(self, renderer):
        """
        Update the label position based on the bounding box enclosing
        all the ticklabels and axis spine
        """
        if not self._autolabelpos:
            return

        # get bounding boxes for this axis and any siblings
        # that have been set by `fig.align_ylabels()`
        bboxes, bboxes2 = self._get_tick_boxes_siblings(renderer=renderer)

        x, y = self.label.get_position()
        if self.label_position == 'left':
            try:
                spine = self.axes.spines['left']
                spinebbox = spine.get_transform().transform_path(
                    spine.get_path()).get_extents()
            except KeyError:
                # use axes if spine doesn't exist
                spinebbox = self.axes.bbox
            bbox = mtransforms.Bbox.union(bboxes + [spinebbox])
            left = bbox.x0
            self.label.set_position(
                (left - self.labelpad * self.figure.dpi / 72, y)
            )

        else:
            try:
                spine = self.axes.spines['right']
                spinebbox = spine.get_transform().transform_path(
                    spine.get_path()).get_extents()
            except KeyError:
                # use axes if spine doesn't exist
                spinebbox = self.axes.bbox
            bbox = mtransforms.Bbox.union(bboxes2 + [spinebbox])
            right = bbox.x1

            self.label.set_position(
                (right + self.labelpad * self.figure.dpi / 72, y)
            )

    def _update_offset_text_position(self, bboxes, bboxes2):
        """
        Update the offset_text position based on the sequence of bounding
        boxes of all the ticklabels
        """
        x, y = self.offsetText.get_position()
        top = self.axes.bbox.ymax
        self.offsetText.set_position(
            (x, top + self.OFFSETTEXTPAD * self.figure.dpi / 72)
        )

    def set_offset_position(self, position):
        """
        Parameters
        ----------
        position : {'left', 'right'}
        """
        x, y = self.offsetText.get_position()
        x = cbook._check_getitem({'left': 0, 'right': 1}, position=position)

        self.offsetText.set_ha(position)
        self.offsetText.set_position((x, y))
        self.stale = True

    def get_text_widths(self, renderer):
        bbox, bbox2 = self.get_ticklabel_extents(renderer)
        # MGDTODO: Need a better way to get the pad
        padPixels = self.majorTicks[0].get_pad_pixels()

        left = 0.0
        if bbox.width:
            left += bbox.width + padPixels
        right = 0.0
        if bbox2.width:
            right += bbox2.width + padPixels

        if self.get_label_position() == 'left':
            left += self.label.get_window_extent(renderer).width + padPixels
        else:
            right += self.label.get_window_extent(renderer).width + padPixels
        return left, right

    def set_ticks_position(self, position):
        """
        Set the ticks position.

        Parameters
        ----------
        position : {'left', 'right', 'both', 'default', 'none'}
            'both' sets the ticks to appear on both positions, but does not
            change the tick labels.  'default' resets the tick positions to
            the default: ticks on both positions, labels at left.  'none'
            can be used if you don't want any ticks. 'none' and 'both'
            affect only the ticks, not the labels.
        """
        cbook._check_in_list(['left', 'right', 'both', 'default', 'none'],
                             position=position)
        if position == 'right':
            self.set_tick_params(which='both', right=True, labelright=True,
                                 left=False, labelleft=False)
            self.set_offset_position(position)
        elif position == 'left':
            self.set_tick_params(which='both', right=False, labelright=False,
                                 left=True, labelleft=True)
            self.set_offset_position(position)
        elif position == 'both':
            self.set_tick_params(which='both', right=True,
                                 left=True)
        elif position == 'none':
            self.set_tick_params(which='both', right=False,
                                 left=False)
        elif position == 'default':
            self.set_tick_params(which='both', right=True, labelright=False,
                                 left=True, labelleft=True)
        else:
            assert False, "unhandled parameter not caught by _check_in_list"
        self.stale = True

    def tick_right(self):
        """
        Move ticks and ticklabels (if present) to the right of the axes.
        """
        label = True
        if 'label1On' in self._major_tick_kw:
            label = (self._major_tick_kw['label1On']
                     or self._major_tick_kw['label2On'])
        self.set_ticks_position('right')
        # if labels were turned off before this was called
        # leave them off
        self.set_tick_params(which='both', labelright=label)

    def tick_left(self):
        """
        Move ticks and ticklabels (if present) to the left of the axes.
        """
        label = True
        if 'label1On' in self._major_tick_kw:
            label = (self._major_tick_kw['label1On']
                     or self._major_tick_kw['label2On'])
        self.set_ticks_position('left')
        # if labels were turned off before this was called
        # leave them off
        self.set_tick_params(which='both', labelleft=label)

    def get_ticks_position(self):
        """
        Return the ticks position ("left", "right", "default", or "unknown").
        """
        return {1: "left", 2: "right",
                "default": "default", "unknown": "unknown"}[
                    self._get_ticks_position()]

    get_view_interval, set_view_interval = _make_getset_interval(
        "view", "viewLim", "intervaly")
    get_data_interval, set_data_interval = _make_getset_interval(
        "data", "dataLim", "intervaly")

    def get_minpos(self):
        return self.axes.dataLim.minposy

    def set_inverted(self, inverted):
        # docstring inherited
        a, b = self.get_view_interval()
        # cast to bool to avoid bad interaction between python 3.8 and np.bool_
        self.axes.set_ylim(sorted((a, b), reverse=bool(inverted)), auto=None)

    def set_default_intervals(self):
        # docstring inherited
        ymin, ymax = 0., 1.
        dataMutated = self.axes.dataLim.mutatedy()
        viewMutated = self.axes.viewLim.mutatedy()
        if not dataMutated or not viewMutated:
            if self.converter is not None:
                info = self.converter.axisinfo(self.units, self)
                if info.default_limits is not None:
                    valmin, valmax = info.default_limits
                    ymin = self.converter.convert(valmin, self.units, self)
                    ymax = self.converter.convert(valmax, self.units, self)
            if not dataMutated:
                self.axes.dataLim.intervaly = ymin, ymax
            if not viewMutated:
                self.axes.viewLim.intervaly = ymin, ymax
        self.stale = True

    def get_tick_space(self):
        ends = self.axes.transAxes.transform([[0, 0], [0, 1]])
        length = ((ends[1][1] - ends[0][1]) / self.axes.figure.dpi) * 72
        # Having a spacing of at least 2 just looks good.
        size = self._get_tick_label_size('y') * 2
        if size > 0:
            return int(np.floor(length / size))
        else:
            return 2**31 - 1
