#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 FlashSummary.py 中的富文本红色字体标注功能
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们修改的模块
import FlashSummary
import PublicFuc
from openpyxl import Workbook
from openpyxl.cell.rich_text import TextBlock, CellRichText
from openpyxl.styles import Font

def test_create_rich_text_with_red_abnormal_fields():
    """测试 CreateRichTextWithRedAbnormalFields 函数"""
    print("=== 测试 CreateRichTextWithRedAbnormalFields 函数 ===")
    
    # 测试用例1：包含异常字段的文本
    test_text1 = "[4P-N58R-02]: C3=864 [4P-N58R-13]: B6=2、C3=E4F、C5=2 [50-BICS8-B11]: C3=2083、C7=80"
    print(f"输入文本: {test_text1}")
    
    rich_text1 = FlashSummary.CreateRichTextWithRedAbnormalFields(test_text1)
    print(f"富文本对象类型: {type(rich_text1)}")
    
    if isinstance(rich_text1, CellRichText):
        print("成功创建富文本对象")
        print(f"文本块数量: {len(rich_text1)}")
        
        # 分析每个文本块
        for i, block in enumerate(rich_text1):
            if hasattr(block, 'font') and hasattr(block, 'text'):
                color = getattr(block.font, 'color', None)
                color_value = getattr(color, 'rgb', 'default') if color else 'default'
                print(f"  块 {i+1}: '{block.text}' - 颜色: {color_value}")
    else:
        print(f"返回普通文本: {rich_text1}")
    print()
    
    # 测试用例2：不包含异常字段的文本
    test_text2 = "[Sample001]: AF=1234、C3=5678、C6=9999、C7=ABCD"
    print(f"输入文本: {test_text2}")
    
    rich_text2 = FlashSummary.CreateRichTextWithRedAbnormalFields(test_text2)
    print(f"富文本对象类型: {type(rich_text2)}")
    
    if isinstance(rich_text2, CellRichText):
        print("成功创建富文本对象")
        print(f"文本块数量: {len(rich_text2)}")
        
        # 分析每个文本块
        for i, block in enumerate(rich_text2):
            if hasattr(block, 'font') and hasattr(block, 'text'):
                color = getattr(block.font, 'color', None)
                color_value = getattr(color, 'rgb', 'default') if color else 'default'
                print(f"  块 {i+1}: '{block.text}' - 颜色: {color_value}")
    else:
        print(f"返回普通文本: {rich_text2}")
    print()
    
    # 测试用例3：空文本
    test_text3 = ""
    print(f"输入文本: '{test_text3}'")
    
    rich_text3 = FlashSummary.CreateRichTextWithRedAbnormalFields(test_text3)
    print(f"返回结果: '{rich_text3}'")
    print()
    
    # 测试用例4：只包含异常字段的文本
    test_text4 = "05=1234、B2=ABCD、B5=5678、B6=9999、C4=EFGH、C5=1111"
    print(f"输入文本: {test_text4}")
    
    rich_text4 = FlashSummary.CreateRichTextWithRedAbnormalFields(test_text4)
    print(f"富文本对象类型: {type(rich_text4)}")
    
    if isinstance(rich_text4, CellRichText):
        print("成功创建富文本对象")
        print(f"文本块数量: {len(rich_text4)}")
        
        # 分析每个文本块
        for i, block in enumerate(rich_text4):
            if hasattr(block, 'font') and hasattr(block, 'text'):
                color = getattr(block.font, 'color', None)
                color_value = getattr(color, 'rgb', 'default') if color else 'default'
                print(f"  块 {i+1}: '{block.text}' - 颜色: {color_value}")
    else:
        print(f"返回普通文本: {rich_text4}")
    print()

def test_excel_rich_text_integration():
    """测试在 Excel 中使用富文本的集成功能"""
    print("=== 测试 Excel 富文本集成功能 ===")
    
    try:
        # 创建一个新的工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "富文本测试"
        
        # 测试文本
        test_text = "[Sample001]: 05=1234、AF=ABCD、B2=EFGH、C3=5678"
        
        # 创建富文本
        rich_text = FlashSummary.CreateRichTextWithRedAbnormalFields(test_text)
        
        # 将富文本写入单元格
        ws['A1'] = rich_text
        
        # 保存文件
        test_file = "test_rich_text_output.xlsx"
        wb.save(test_file)
        
        print(f"成功创建测试文件: {test_file}")
        print(f"单元格 A1 内容类型: {type(ws['A1'].value)}")
        
        # 清理
        wb.close()
        
        # 尝试删除测试文件
        try:
            os.remove(test_file)
            print("已清理测试文件")
        except:
            print("无法删除测试文件，请手动清理")
            
    except Exception as e:
        print(f"Excel 集成测试失败: {e}")
    
    print()

def test_abnormal_field_detection():
    """测试异常字段检测的准确性"""
    print("=== 测试异常字段检测准确性 ===")
    
    test_cases = [
        ("05=1234", True, "05是异常字段"),
        ("AF=ABCD", False, "AF不是异常字段"),
        ("B2=EFGH", True, "B2是异常字段"),
        ("B5=5678", True, "B5是异常字段"),
        ("B6=9999", True, "B6是异常字段"),
        ("C3=1111", False, "C3不是异常字段"),
        ("C4=2222", True, "C4是异常字段"),
        ("C5=3333", True, "C5是异常字段"),
        ("C6=4444", False, "C6不是异常字段"),
        ("C7=5555", False, "C7不是异常字段"),
    ]
    
    for test_input, expected, description in test_cases:
        has_abnormal = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_input)
        status = "✓" if has_abnormal == expected else "✗"
        print(f"{status} {test_input} -> {has_abnormal} ({description})")
    
    print()

def test_regex_pattern_matching():
    """测试正则表达式模式匹配"""
    print("=== 测试正则表达式模式匹配 ===")
    
    import re
    
    # 使用与 CreateRichTextWithRedAbnormalFields 相同的正则表达式
    pattern = r'([A-F0-9]+)=([A-F0-9]+)'
    
    test_texts = [
        "05=1234",
        "AF=ABCD",
        "B2=EFGH、C3=5678",
        "[Sample001]: 05=1234、AF=ABCD",
        "C3=864 B6=2、C5=2",
        "invalid=text",  # 应该不匹配
        "123=ABC",       # 应该匹配
    ]
    
    for text in test_texts:
        matches = list(re.finditer(pattern, text))
        print(f"文本: '{text}'")
        print(f"匹配数量: {len(matches)}")
        for i, match in enumerate(matches):
            field_id = match.group(1).upper()
            value = match.group(2)
            full_match = match.group(0)
            is_abnormal = field_id in PublicFuc.flashSummaryAbnormalKey
            print(f"  匹配 {i+1}: {full_match} (字段: {field_id}, 值: {value}, 异常: {is_abnormal})")
        print()

if __name__ == "__main__":
    print("开始测试 FlashSummary.py 富文本红色字体标注功能\n")
    
    test_abnormal_field_detection()
    test_regex_pattern_matching()
    test_create_rich_text_with_red_abnormal_fields()
    test_excel_rich_text_integration()
    
    print("测试完成！")
    print("\n=== 总结 ===")
    print("1. 已将整个单元格背景标红改为对特定文字进行红色字体标注")
    print("2. 使用 openpyxl 的 CellRichText 和 TextBlock 实现富文本功能")
    print("3. 只对异常 SMART 字段（05、B2、B5、B6、C4、C5）进行红色标注")
    print("4. 保持文本的其他格式（如换行、分隔符等）不变")
    print("5. 适用于主页第5-14行L列的所有SMART信息单元格")
    print("6. 仅影响 Flash品质测试用例及报告汇总.xlsx 文件")
