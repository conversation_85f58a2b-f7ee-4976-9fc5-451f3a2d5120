
# This file was generated by 'versioneer.py' (0.15) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json
import sys

version_json = '''
{
 "dirty": false,
 "error": null,
 "full-revisionid": "6e4d72c663c9930115720ac469341ed56a9505ec",
 "version": "3.3.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
