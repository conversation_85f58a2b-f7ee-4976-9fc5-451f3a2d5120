import configparser
import os,re,time,logging

from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment
from datetime import datetime,timedelta
import csv

strSsdTempDir = '' #唯一的临时文件目录

testno = '' #测试单号，用于从数据库中统计睡眠和休眠信息
minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
alignmentNewLine = Alignment(horizontal='center',vertical='center',wrap_text=True)
warnFill = PatternFill('solid', fgColor='FF0000')
commonSmartKey = ['F1','F2','A5','A6','05','0C','A3','A4','A7','AF','B2','B5','B6','C0','C3','C4','C5','C6','C7','B0','B1']
commonSmartKeyEx = ['F1','F2','A5','A6','05','0C','AF','B2','B5','B6','C0','C3','C4','C5','C6','C7','B0','B1']
# 专门用于Flash品质测试用例及报告汇总.xlsx的SMART字段过滤
flashSummarySmartKey = ['05','AF','B2','B5','B6','C3','C4','C5','C6','C7']
# Flash品质测试用例及报告汇总.xlsx中需要标红的异常SMART字段
flashSummaryAbnormalKey = ['05','B2','B5','B6','C4','C5']
errDiskLst = []
marsKey = ['test_result','end_por','start_por','end_id_f1','start_id_f1','end_id_f2','start_id_f2','end_circle','start_circle','end_time','start_time',\
            'average_write_vel','max_write_vel','min_write_vel','average_read_vel','max_read_vel','min_read_vel','write_overtime']
marsKeyEx = ['ID_A5','ID_A6','ID_05','ID_0C','ID_AF','ID_B2','ID_B5','ID_B6','ID_C0','ID_C3','ID_C4','ID_C5','ID_C6','ID_C7','ID_B0','ID_B1']
for item in commonSmartKey:
    smart = 'id_'+item.lower()
    marsKey.append(smart)

fileLst = []
config = configparser.RawConfigParser()
def GetAllFile(curpath):
    global testno
    for dirpath,dirnames,filenames in os.walk(curpath):
        if testno == '':
            idx = dirpath.find('testno_')
            if idx != -1:
                testno = dirpath[idx+len('testno_'):len(dirpath)]
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)

def WriteErrDiskFile(strFile):
    if 0 != len(errDiskLst):
        with open(strFile,'w+') as file: 
            for errLst in errDiskLst:
                strErr = '样片:%s    PC:%s    Err:%s    Time:%s\n'%(errLst[0],errLst[1],errLst[2],errLst[3])
                file.write(strErr)


def ReadQaIniData(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
            tempLst = []
            pcNo = ''
            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                    if 'qa_err_msg' == key:
                        filemt= time.localtime(os.stat(file).st_mtime)  
                        strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                        errDiskLst.append([sec,pcNo,value,strTime])
                else:
                    tempLst.append('')
            if len(dataDic[sec]) < recordCnt and [] != tempLst:
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec].append(tempLst)

def ReadQaIniDataEx(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 10):
    unitLst = ['MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                fileMdTime = os.path.getmtime(file)
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    tempLst.append(fileMdTime)
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                    dataDic[sec].append(tempLst)
            else:
                fileMdTime = os.path.getmtime(file)
                if dataDic[sec][0][-2] > fileMdTime:
                    continue
                dataDic[sec] = []
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    tempLst.append(fileMdTime)
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                    dataDic[sec].append(tempLst)

def ReadMarsIniData(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            fileMdTime = os.path.getmtime(file)
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName]['fileMdTime'] = fileMdTime
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                     if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])
        else:
            fileMdTime = os.path.getmtime(file)
            if dataDic[keyName]['fileMdTime'] > fileMdTime:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName]['fileMdTime'] = fileMdTime
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])

def ReadMarsIniDataWithIniPath(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            fileMdTime = os.path.getmtime(file)
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName]['fileMdTime'] = fileMdTime
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            dataDic[keyName]['ini_path'] = file
            if 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])
        else:
            fileMdTime = os.path.getmtime(file)
            if dataDic[keyName]['fileMdTime'] > fileMdTime:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName]['fileMdTime'] = fileMdTime
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            dataDic[keyName]['ini_path'] = file
            if 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])

def ReadIdleIniData(curpath, pattern, dataDic, caseName, recordCnt = 2):
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        # 必须清除
        config.clear()
        config.read(file, encoding='gbk')
        # logging.info(file)
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            # 0表示样片数量不确定，需要获取全部数据
            fileMdTime = os.path.getmtime(file)
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName]['fileMdTime'] = fileMdTime
            for key in marsKeyEx:
                if key not in config[caseName]:
                    dataDic[keyName][key] = ''
                    continue
                value = config[caseName][key]
                dataDic[keyName][key] = value
        else:
            fileMdTime = os.path.getmtime(file)
            if dataDic[keyName]['fileMdTime'] > fileMdTime:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            for key in marsKeyEx:
                if key not in config[caseName]:
                    dataDic[keyName][key] = ''
                    continue
                value = config[caseName][key]
                dataDic[keyName][key] = value

        csvFile = os.path.join(os.path.abspath(os.path.join(file, '../..')), 'AS_SSD_Info.csv')
        if os.path.isfile(csvFile):
            with open(csvFile, 'r') as csvfile:
                csv_reader = csv.reader(csvfile)
                next(csv_reader)
                csv_data = {'seqRead':[], 'seqWrite':[], 'k4Read':[], 'k4Write':[], 'k4ThrdRead':[], 'k4ThrdWrite':[], 'accRead':[], 'accWrite':[], 'scoreRead':[], 'scoreWrite':[], 'scoreSum':[]}
                for row in csv_reader:
                    csv_data['seqRead'].append(float(row[5]))
                    csv_data['seqWrite'].append(float(row[6]))
                    csv_data['k4Read'].append(float(row[7]))
                    csv_data['k4Write'].append(float(row[8]))
                    csv_data['k4ThrdRead'].append(float(row[9]))
                    csv_data['k4ThrdWrite'].append(float(row[10]))
                    csv_data['accRead'].append(float(row[11]))
                    csv_data['accWrite'].append(float(row[12]))
                    csv_data['scoreRead'].append(float(row[13]))
                    csv_data['scoreWrite'].append(float(row[14]))
                    csv_data['scoreSum'].append(float(row[15]))
                dataDic[keyName]['csvData'] = csv_data
        else:
            dataDic[keyName]['csvData'] = ''

def ReadIdleIniDataEX(curpath, pattern, dataDic, caseName, recordCnt = 2):
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pathFile = os.path.abspath(os.path.join(file, '../../../..'))
        folderName = ''
        for folder in os.listdir(pathFile):
            if folder.startswith('Mars_'):
                folderName = folder

        config.clear()
        config.read(file, encoding='gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if recordCnt == len(dataDic) and 0 != recordCnt:
                    continue
                dataDic[sec] = {}
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                dataDic[sec]['MMS_PC'] = pcNo
                cap = ''
                if 'Cap' in config[sec]:
                    cap = config[sec]['Cap']
                dataDic[sec]['capacity'] = cap
                fileMdTime = os.path.getmtime(file)
                dataDic[sec]['fileMdTime'] = fileMdTime
                for key in commonSmartKeyEx[2:]:
                    if key not in config[sec]:
                        dataDic[sec]['ID_'+key] = ''
                        continue
                    value = config[sec][key]
                    dataDic[sec]['ID_'+key] = value.lstrip('0')
            else:
                fileMdTime = os.path.getmtime(file)
                if dataDic[sec]['fileMdTime'] > fileMdTime:
                    continue
                dataDic[sec] = {}
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                dataDic[sec]['MMS_PC'] = pcNo
                cap = ''
                if 'Cap' in config[sec]:
                    cap = config[sec]['Cap']
                dataDic[sec]['capacity'] = cap
                fileMdTime = os.path.getmtime(file)
                dataDic[sec]['fileMdTime'] = fileMdTime
                for key in commonSmartKeyEx[2:]:
                    if key in config[sec]:
                        dataDic[sec]['ID_' + key] = ''
                        continue
                    value = config[sec][key]
                    dataDic[sec]['ID_' + key] = value.lstrip('0')

            csvFile = ''
            for filename in os.listdir(os.path.join(pathFile, folderName, 'log')):
                # 检查文件名是否包含目标字符串
                if sec in filename:
                    # 拼接完整路径
                    csvFile = os.path.join(pathFile, folderName, 'log', filename, '1', 'AS_SSD_Info.csv')
                    break

            if os.path.isfile(csvFile):
                with open(csvFile, 'r') as csvfile:
                    csv_reader = csv.reader(csvfile)
                    next(csv_reader)
                    csv_data = {'seqRead':[], 'seqWrite':[], 'k4Read':[], 'k4Write':[], 'k4ThrdRead':[], 'k4ThrdWrite':[], 'accRead':[], 'accWrite':[], 'scoreRead':[], 'scoreWrite':[], 'scoreSum':[]}
                    for row in csv_reader:
                        csv_data['seqRead'].append(float(row[5]))
                        csv_data['seqWrite'].append(float(row[6]))
                        csv_data['k4Read'].append(float(row[7]))
                        csv_data['k4Write'].append(float(row[8]))
                        csv_data['k4ThrdRead'].append(float(row[9]))
                        csv_data['k4ThrdWrite'].append(float(row[10]))
                        csv_data['accRead'].append(float(row[11]))
                        csv_data['accWrite'].append(float(row[12]))
                        csv_data['scoreRead'].append(float(row[13]))
                        csv_data['scoreWrite'].append(float(row[14]))
                        csv_data['scoreSum'].append(float(row[15]))
                    dataDic[sec]['csvData'] = csv_data
            else:
                dataDic[sec]['csvData'] = ''

def ReadIdleConfigData(curpath, pattern, dataDic, caseName, recordCnt = 2):
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pathFile =os.path.abspath(os.path.join(file, '../../..'))
        fileName = os.path.basename(pathFile)
        keyName = fileName[14:]
            # 必须清除
        config.clear()
        config.read(file, encoding='gbk')
        # logging.info(file)
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue

        if keyName not in dataDic:
            # 0表示样片数量不确定，需要获取全部数据
            fileMdTime = os.path.getmtime(file)
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            dataDic[keyName]['fileMdTime'] = fileMdTime
            for key in marsKeyEx:
                if key not in config[caseName]:
                    dataDic[keyName][key] = ''
                    continue
                value = config[caseName][key]
                dataDic[keyName][key] = value[2:]
        else:
            fileMdTime = os.path.getmtime(file)
            if dataDic[keyName]['fileMdTime'] > fileMdTime:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
            for key in marsKeyEx:
                if key not in config[caseName]:
                    dataDic[keyName][key] = ''
                    continue
                value = config[caseName][key]
                dataDic[keyName][key] = value[2:]

        csvFile = os.path.join(os.path.abspath(os.path.join(file, '../..')), 'AS_SSD_Info.csv')
        if os.path.isfile(csvFile):
            with open(csvFile, 'r') as csvfile:
                csv_reader = csv.reader(csvfile)
                next(csv_reader)
                csv_data = {'seqRead': [], 'seqWrite': [], 'k4Read': [], 'k4Write': [], 'k4ThrdRead': [],
                            'k4ThrdWrite': [], 'accRead': [], 'accWrite': [], 'scoreRead': [], 'scoreWrite': [],
                            'scoreSum': []}
                for row in csv_reader:
                    csv_data['seqRead'].append(float(row[5]))
                    csv_data['seqWrite'].append(float(row[6]))
                    csv_data['k4Read'].append(float(row[7]))
                    csv_data['k4Write'].append(float(row[8]))
                    csv_data['k4ThrdRead'].append(float(row[9]))
                    csv_data['k4ThrdWrite'].append(float(row[10]))
                    csv_data['accRead'].append(float(row[11]))
                    csv_data['accWrite'].append(float(row[12]))
                    csv_data['scoreRead'].append(float(row[13]))
                    csv_data['scoreWrite'].append(float(row[14]))
                    csv_data['scoreSum'].append(float(row[15]))
                dataDic[keyName]['csvData'] = csv_data
        else:
            dataDic[keyName]['csvData'] = ''

def ReadPorIniData(curpath, pattern, dataDic, keyLst, recordCnt = 10, diskCnt = 2):
    unitLst = ['MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
            tempLst = []
            pcNo = ''
            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')
            if len(dataDic[sec]) < recordCnt :
                dataDic[sec].append(tempLst)

def WriteDataAndImage(worksheet, startLine, dataDic, colLst, keyLst, imgWidth, imgHeight):
    imageLine = startLine+20
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        #最后一行数据是经过处理获取的最值
        limitLine = dataDic[key][-1]
        for line in dataDic[key][:-1]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    try:
                        floatValue = float(line[index-1])
                        #和最值相差20%需要填充红色警告
                        if keyLst[index-1] not in minKey:
                            if floatValue <= limitLine[index-1]*0.8:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                        else:
                            if floatValue >= limitLine[index-1]*1.2:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    except:
                        continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+10
        imageLine += 1
#1个样片多条记录
def WriteData(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                        if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                            if line[index-1] >= 400:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    worksheet['%s%d'%(col, curLine)].alignment = alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += lineCnt
            startLine += 1
    return startLine
#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteDataNormal(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    sortedKeys = sorted(dataDic.keys())
    for key in sortedKeys:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                        if line[index-1] >= 400:
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                    if index-1 < len(keyLst) and keyLst[index-1] == 'result':
                        if line[index-1] != '' and line[index-1] !='TRUE':
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += lineCnt

#获取每个编号每列数据的最值
def GetMaxOrMinValueLst(keyLst,dataDic):
    for key in dataDic:
        resultLst = []
        for index,col in enumerate(keyLst):
            tempLst = [line[index] for line in dataDic[key]]
            limitData = 0
            bFirstData = True
            for data in tempLst:
                try:
                    tempData = float(data)
                    #部分列需要取最小值，例如时间等
                    if bFirstData:
                        limitData = tempData
                        bFirstData = False
                        continue
                    if col in minKey:
                        if tempData < limitData:
                            limitData = tempData
                    else:
                        if tempData > limitData:
                            limitData = tempData
                except:
                    continue
            resultLst.append(limitData)
        dataDic[key].append(resultLst)

def FmtStrHex(strHex):
    #去掉十六进制前面的多个0
    strNew = strHex.lstrip('0')
    if '' == strNew:
        strNew = '0'
    return strNew

def GetImtResultPath(strImtBmpPath, key):
    pos = strImtBmpPath.rfind('\\')
    strPath = strImtBmpPath[:pos+1] + 'inst%s_IometerResults.csv'%key
    return strPath

def GetNewIoMeterDic(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-1:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewATACTDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            # F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0], 16) * 32) // 1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1], 16) * 32) // 1024)
            strSmart = ''
            for idx, value in enumerate(smartLst[2:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[2+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic

def GetNewPorDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            # F1、F2 1个单位为32M，需转化为G
            strSmart = ''
            for idx, value in enumerate(smartLst):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[0] == '' or smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[0],16)-int(smartLst[1],16))
            newDic[key].append(newLst)
    return newDic

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result'])
        if '' == dic['end_circle'] or '' == dic['start_circle']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['end_circle'])-int(dic['start_circle']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['max_write_vel'])
        newDic[key].append(dic['min_write_vel'])
        newDic[key].append(dic['average_read_vel'])
        newDic[key].append(dic['max_read_vel'])
        newDic[key].append(dic['min_read_vel'])
        newDic[key].append(dic['write_overtime'])
        if '' == dic['id_a5'] or '' == dic['id_a6']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def GetNewBitDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G)/F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            write = 0
            if smartLst[0] != '':
                write = (int(smartLst[0],16)*32)//1024
            read = 0
            if smartLst[1] != '':
                read = (int(smartLst[1],16)*32)//1024
            newLst.append('%d/%d'%(write,read))
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic


#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName


def ReadRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                row.append(file)
                dataDic.append(row)

# ========== Flash品质测试用例及报告汇总.xlsx 专用函数 ==========

def FilterSmartInfoForFlashSummary(smartStr):
    """
    专门用于Flash品质测试用例及报告汇总.xlsx的SMART信息过滤函数
    只保留指定的字段：05、AF、B2、B5、B6、C3、C4、C5、C6、C7
    """
    if not smartStr or smartStr.strip() == '':
        return ''

    # 分割SMART信息字符串，通常格式为 "05=xxx,AF=yyy,B2=zzz"
    smart_pairs = smartStr.split(',')
    filtered_pairs = []

    for pair in smart_pairs:
        pair = pair.strip()
        if '=' in pair:
            field_id = pair.split('=')[0].strip().upper()
            if field_id in flashSummarySmartKey:
                filtered_pairs.append(pair)

    return ','.join(filtered_pairs)

def HasAbnormalSmartFieldsForFlashSummary(smartStr):
    """
    专门用于Flash品质测试用例及报告汇总.xlsx的异常SMART字段检测函数
    检测SMART信息中是否包含异常字段：05、B2、B5、B6、C4、C5
    返回True表示包含异常字段，需要红色标注
    """
    if not smartStr or smartStr.strip() == '':
        return False

    # 分割SMART信息字符串
    smart_pairs = smartStr.split(',')

    for pair in smart_pairs:
        pair = pair.strip()
        if '=' in pair:
            field_id = pair.split('=')[0].strip().upper()
            if field_id in flashSummaryAbnormalKey:
                # 检查值是否不为0（表示异常）
                try:
                    value_str = pair.split('=')[1].strip()
                    if value_str and value_str != '0' and value_str != '00':
                        return True
                except:
                    continue

    return False

def GetNewMarsDicForFlashSummary(oldDic, keyLst):
    """
    专门用于Flash品质测试用例及报告汇总.xlsx的GetNewMarsDic函数
    在SMART信息处理中应用过滤逻辑
    """
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result'])
        if '' == dic['end_circle'] or '' == dic['start_circle']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['end_circle'])-int(dic['start_circle']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        smart = ''
        #统计不为0的smart信息，并应用Flash品质测试用例及报告汇总.xlsx的过滤逻辑
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    # 只保留Flash品质测试用例及报告汇总.xlsx指定的SMART字段
                    if id in flashSummarySmartKey and id in commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['max_write_vel'])
        newDic[key].append(dic['min_write_vel'])
        newDic[key].append(dic['average_read_vel'])
        newDic[key].append(dic['max_read_vel'])
        newDic[key].append(dic['min_read_vel'])
        newDic[key].append(dic['write_overtime'])
        if '' == dic['id_a5'] or '' == dic['id_a6']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic