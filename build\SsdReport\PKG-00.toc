('G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\build\\SsdReport\\PKG-00.pkg',
 {'BINARY': 1,
  'DATA': 1,
  'EXECUTABLE': 1,
  'EXTENSION': 1,
  'PYMODULE': 1,
  'PYSOURCE': 1,
  'PYZ': 0},
 [('PYZ-00.pyz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\build\\SsdReport\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\struct.pyo',
   'PYMODULE'),
  ('pyimod01_os_path',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\loader\\pyimod01_os_path.pyc',
   'PYMODULE'),
  ('pyimod02_archive',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\loader\\pyimod02_archive.pyc',
   'PYMODULE'),
  ('pyimod03_importers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\loader\\pyimod03_importers.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_mpldata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mpldata.py',
   'PYSOURCE'),
  ('pyi_rth_certifi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_certifi.py',
   'PYSOURCE'),
  ('SsdReport',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\SsdReport.py',
   'PYSOURCE'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\VCRUNTIME140.dll',
   'BINARY'),
  ('python37.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\python37.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('pythoncom37.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pywin32_system32\\pythoncom37.dll',
   'BINARY'),
  ('pywintypes37.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('libopenblas.NOIJJG62EMASZI6NYURL6JBKM4EVBGM7.gfortran-win_amd64.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\.libs\\libopenblas.NOIJJG62EMASZI6NYURL6JBKM4EVBGM7.gfortran-win_amd64.dll',
   'BINARY'),
  ('select',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_socket.pyd',
   'EXTENSION'),
  ('_ssl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_ctypes.pyd',
   'EXTENSION'),
  ('win32api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com.shell.shell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32trace',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32ui',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('_hashlib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_hashlib.pyd',
   'EXTENSION'),
  ('win32wnet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('_lzma',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_queue',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_queue.pyd',
   'EXTENSION'),
  ('pyexpat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\pyexpat.pyd',
   'EXTENSION'),
  ('_contextvars',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_contextvars.pyd',
   'EXTENSION'),
  ('_decimal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\unicodedata.pyd',
   'EXTENSION'),
  ('_elementtree',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_elementtree.pyd',
   'EXTENSION'),
  ('_distutils_findvs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_distutils_findvs.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32pdh',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingtk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._webp',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._path',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_path.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib.ft2font',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\ft2font.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._ttconv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_ttconv.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_tkinter.pyd',
   'EXTENSION'),
  ('matplotlib.backends._tkagg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_tkagg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib.backends._backend_agg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._contour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_contour.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._tri',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_tri.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._qhull',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_qhull.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\kiwisolver.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_image.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.writers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\writers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.json',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\json.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.parsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.strptime',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\ops.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.index',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\index.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reshape',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\reshape.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.indexers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.np_datetime',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.dtypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.conversion',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.internals',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\internals.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.aggregations',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.nattype',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.ccalendar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timedeltas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.period',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.tzconversion',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.offsets',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timestamps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.missing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\missing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.indexing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\indexing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reduction',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\reduction.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.fields',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.vectorized',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\properties.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\hashing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.interval',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\interval.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.parsers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\parsers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.algos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\algos.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops_dispatch',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timezones',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\testing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.join',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\join.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.groupby',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\groupby.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.sparse',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\sparse.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas.io.sas._sas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\tslib.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.lib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\lib.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashtable',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\hashtable.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\_sqlite3.pyd',
   'EXTENSION'),
  ('pymssql',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pymssql.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_mssql',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_mssql.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('libssl-1_1-x64.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\libssl-1_1-x64.dll',
   'BINARY'),
  ('mfc140u.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('tcl86t.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\tk86t.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('sqlite3.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\sqlite3.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\build\\SsdReport\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.gif',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\demodata.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\demodata.csv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.gif',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tk\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('lib2to3\\PatternGrammar.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\PatternGrammar.txt',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\ct.raw.gz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\ct.raw.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('lib2to3\\Grammar3.7.0.final.0.pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\Grammar3.7.0.final.0.pickle',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('certifi\\cacert.pem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('tk\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.gif',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand_large.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('Include\\pyconfig.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\Include\\pyconfig.h',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('lib2to3\\PatternGrammar3.7.0.final.0.pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\PatternGrammar3.7.0.final.0.pickle',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.png',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.ppm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.ppm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\ada.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\ada.png',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\None_vs_nearest-pdf.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\None_vs_nearest-pdf.png',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('lib2to3\\tests\\data\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\tests\\data\\README',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('tcl\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.gif',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tk\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tk\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\aapl.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\aapl.npz',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.ppm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.ppm',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('tcl\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tk\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('lib2to3\\Grammar.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\Grammar.txt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.gif',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('tcl\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('SsdReport.exe.manifest',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\build\\SsdReport\\SsdReport.exe.manifest',
   'BINARY'),
  ('pyi-windows-manifest-filename SsdReport.exe.manifest', '', 'OPTION')],
 False,
 False,
 False,
 [])
