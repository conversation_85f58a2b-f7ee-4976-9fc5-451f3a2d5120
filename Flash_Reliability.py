import PublicFuc
from datetime import datetime,timedelta
curLine1 = 11
curLine2 = 10
curRecord1 = 200
curRecord2 =100
def Run(curpath, workBook, alignment):
    PraseRunPlan(curpath, workBook, alignment, 'A1_高温老化_48H', 'BurnInTest48H', 'T-SS-SS-E02', 'Plan502')
    PraseRunPlan(curpath, workBook, alignment, 'B1_高温老化_72H', 'BurnInTest72H', 'T-SS-SS-E13', 'Plan511')
    PraseRunPlan(curpath, workBook, alignment, 'C1_常温老化', 'BurnInTest12H', 'T-SS-SS-E19', 'Plan514')
    PraseRunPlan(curpath, workBook, alignment, 'C1_常温老化', 'BurnInTest24H', 'T-SS-SS-E20', 'Plan515')
    PraseRunPlan(curpath, workBook, alignment, 'C1_常温老化', 'BurnInTest48H', 'T-SS-SS-E21', 'Plan516')
    PraseRunPlan(curpath, workBook, alignment, 'C1_常温老化', 'BurnInTest72H', 'T-SS-SS-E22', 'Plan517')

    PraseRunPlan(curpath, workBook, alignment, 'A2_高温ReadDisturb_48H', 'ReadDisturb48H', 'T-SS-SS-E01', 'Plan501')
    PraseRunPlan(curpath, workBook, alignment, 'B2_高温ReadDisturb_72H', 'ReadDisturb72H', 'T-SS-SS-A26', 'Plan510')
    PraseRunPlan(curpath, workBook, alignment, 'C2_常温ReadDisturb', 'ReadDisturb24H', 'T-SS-SS-E23', 'Plan518')
    PraseRunPlan(curpath, workBook, alignment, 'C2_常温ReadDisturb', 'ReadDisturb48H', 'T-SS-SS-E24', 'Plan519')
    PraseRunPlan(curpath, workBook, alignment, 'C2_常温ReadDisturb', 'ReadDisturb72H', 'T-SS-SS-E25', 'Plan520')

    PraseRunPlan(curpath, workBook, alignment, 'A3_高温数据保持_逻辑盘')
    PraseRunPlan(curpath, workBook, alignment, 'B3_高温数据保持_物理盘')
    PraseRunPlan(curpath, workBook, alignment, 'A4_高写低读、低写高读_逻辑盘')

def PraseRunPlan(curpath, workBook, alignment, sheetName, caseName='', caseId='', planName=''):
    ws = workBook[sheetName]
    ws.alignment = alignment
    if '老化' in sheetName:
        ProMarsCase1(curpath, ws, caseName, caseId, planName)
    elif 'ReadDisturb' in sheetName:
        ProMarsCase2(curpath, ws, caseName, caseId, planName)
    else:
        ProMarsCase3(curpath, ws)
    PublicFuc.WriteReportTime(ws, 'W', 2)
    PublicFuc.WriteReportOperator(ws, 'D', 2)



def proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt = 2):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName, recordCnt)
    #GetNewMarsDicForFlashSummary把ini读出来的dic按照keyLst转换为模板所需数据列表，专门用于Flash品质测试用例及报告汇总.xlsx
    newDic = PublicFuc.GetNewMarsDicForFlashSummary(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
def proMarsCommonMutil1(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt = 2):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName, recordCnt)
    #GetNewMarsDicForFlashSummary把ini读出来的dic按照keyLst转换为模板所需数据列表，专门用于Flash品质测试用例及报告汇总.xlsx
    newDic = PublicFuc.GetNewMarsDicForFlashSummary(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
    global curLine1, curRecord1
    curLine1 = startLine + len(newDic)
    curRecord1 -= len(newDic)

def proMarsCommonMutil2(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt = 2):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName, recordCnt)
    #GetNewMarsDicForFlashSummary把ini读出来的dic按照keyLst转换为模板所需数据列表，专门用于Flash品质测试用例及报告汇总.xlsx
    newDic = PublicFuc.GetNewMarsDicForFlashSummary(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
    global curLine2, curRecord2
    curLine2 = startLine + len(newDic)
    curRecord2 -= len(newDic)

def ProMarsCase1(curpath, worksheet, caseName, caseId, plan):
    keyLst = ['pc_no','cap','result','cycle','wrdata','runtime','SmartInfo','wavg','wmax','wmin','ravg','rmax','rmin','wovertime','A5-A6']
    colLst = ['C','B','E','U','H','I','J','K','L','M','N','O','P','Q','R','T']
    pattern = '.+\\\\{}\\\\{}\\\\Mars\\\\\d{{14}}\\\\.+\\\\report\\\\mms.+.ini$'.format(plan, caseId)
    if plan[-2:] < '14':
        startLine = 8
        recordCnt = 100
        proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt)
    else:
        statLine = curLine1
        recordCnt = curRecord1
        proMarsCommonMutil1(curpath, worksheet, pattern, keyLst, colLst, caseName, statLine, recordCnt)

def ProMarsCase2(curpath, worksheet, caseName, caseId, plan):
    keyLst = ['pc_no','cap','result','cycle','wrdata','runtime','SmartInfo','wavg','wmax','wmin','ravg','rmax','rmin','wovertime','A5-A6']
    colLst = ['C','B','E','V','I','J','K','S','L','M','N','O','P','Q','R','U']
    pattern = '.+\\\\{}\\\\{}\\\\Mars\\\\\d{{14}}\\\\.+\\\\report\\\\mms.+.ini$'.format(plan, caseId)
    if plan[-2:] < '18':
        statLine = 8
        recordCnt = 100
        proMarsCommon(curpath, worksheet, pattern, keyLst, colLst, caseName, statLine, recordCnt)
    else:
        startLine = curLine2
        recordCnt = curRecord2
        proMarsCommonMutil2(curpath, worksheet, pattern, keyLst, colLst, caseName, startLine, recordCnt)

def ProMarsCase3(curpath, worksheet):
    #高温保持
    switcher = {'A3_高温数据保持_逻辑盘':lambda:FillRetentionReport(worksheet,curpath,100),
              'B3_高温数据保持_物理盘':lambda:FillRetention1Report(worksheet,curpath,100),
              'A4_高写低读、低写高读_逻辑盘':lambda:FillRetention2Report(worksheet,curpath,10)}
    func = switcher.get(worksheet.title, lambda: None)
    func()

def FillRetentionReport(worksheet,curpath,recordCnt):
    pattern = '.+\\\\Plan508\\\\T-SS-SS-E09\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    startLine = 8
    caseDicH2before = {} #高温前的数据
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    #pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    #pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no','cap','result','wavgH2','ravgH2','SmartInfo','pc_no','ravg1','SmartInfo','A5-A6']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before,caseDicH2verify1,caseDicH2verify2,caseDicH2verify3)
    colLst = ['B','E','C','U','F','G','K','H','I','O','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def FillRetention1Report(worksheet,curpath,recordCnt):
    pattern = '.+\\\\Plan512\\\\T-SS-SS-E14\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'FullDiskWrRd'
    startLine = 8
    caseDicH2before = {} #高温前的数据
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan513\\\\T-SS-SS-E15\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_FullDiskRead' #此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    #pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    #pattern = '.+\\\\Plan509\\\\T-SS-SS-E10\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no','cap','result','wavgH2','ravgH2','SmartInfo','pc_no','ravg1','SmartInfo','A5-A6']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before,caseDicH2verify1,caseDicH2verify2,caseDicH2verify3)
    colLst = ['B','E','C','U','F','G','K','H','I','O','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

#低写高读和高写低读
def FillRetention2Report(worksheet,curpath,recordCnt):
    pattern = '.+\\\\Plan504\\\\T-SS-SS-E05\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    startLine = 22
    caseDicH2before = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan505\\\\T-SS-SS-E06\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    #pattern = '.+\\\\Plan505\\\\T-SS-SS-E06\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    #pattern = '.+\\\\Plan505\\\\T-SS-SS-E06\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    #PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no','cap','result','wavgH2','ravgH2','SmartInfo','pc_no','ravg1','SmartInfo','A5-A6']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before,caseDicH2verify1,caseDicH2verify2,caseDicH2verify3)
    colLst = ['B','E','C','U','F','G','K','H','I','O','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

    pattern = '.+\\\\Plan506\\\\T-SS-SS-E07\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    startLine = 9
    caseDicH2before = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2before, caseName, recordCnt)

    pattern = '.+\\\\Plan507\\\\T-SS-SS-E08\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2Verify'  # 此时只包含校验
    caseDicH2verify1 = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify1, caseName, recordCnt)

    # pattern = '.+\\\\Plan507\\\\T-SS-SS-E08\\\\AT_H2Verify2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    # caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify2 = {}
    # PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify2, caseName, recordCnt)

    # pattern = '.+\\\\Plan507\\\\T-SS-SS-E08\\\\AT_H2Verify3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    # caseName = 'AT_H2Verify' #此时只包含校验
    caseDicH2verify3 = {}
    # PublicFuc.ReadMarsIniData(curpath, pattern, caseDicH2verify3, caseName, recordCnt)

    keyLst = ['pc_no', 'cap', 'result', 'wavgH2', 'ravgH2', 'SmartInfo', 'pc_no', 'ravg1', 'SmartInfo',
              'A5-A6']  # 第一次也显示磨损差值
    # GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewRetentionReportDic(caseDicH2before, caseDicH2verify1, caseDicH2verify2, caseDicH2verify3)
    colLst = ['B', 'E', 'C', 'U', 'F', 'G', 'K', 'H', 'I', 'O', 'T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def GetNewRetentionReportDic(h2Dic, h2verify1Dic, h2verify2Dic, h2verify3Dic):
    newDic = {}
    for key in h2Dic:
        newDic[key] = []
        dic = h2Dic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result'])
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['average_read_vel'])

        smart = ''
        #统计不为0的smart信息，使用Flash品质测试用例及报告汇总.xlsx专用过滤逻辑
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    # 只保留Flash品质测试用例及报告汇总.xlsx指定的SMART字段
                    if id in PublicFuc.flashSummarySmartKey and id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)

        if key in h2verify1Dic:#如果键值在里面
            newDic[key].append(h2verify1Dic[key]['MMS_PC'])
            newDic[key].append(h2verify1Dic[key]['average_read_vel'])
            if newDic[key][2] == '':
                newDic[key][2] = h2verify1Dic[key]['test_result'] #如果前面的没错，则用后面的错误信息填充
        else:
            newDic[key].append('')
            newDic[key].append('')

        # if key in h2verify2Dic:
        #     newDic[key].append(h2verify2Dic[key]['average_read_vel'])
        #     if newDic[key][2] == '':
        #         newDic[key][2] = h2verify2Dic[key]['test_result'] #如果前面的没错，则用后面的错误信息填充
        # else:
        #     newDic[key].append('')
        #
        # newDic[key].append('')#第3次校验无
        if key in h2verify1Dic:
            dic = h2verify1Dic[key]
            smart = ''
            #统计不为0的smart信息，使用Flash品质测试用例及报告汇总.xlsx专用过滤逻辑
            for innerKey in dic.keys():
                if innerKey.startswith('id_'):
                    if '' == dic[innerKey]:
                        continue
                    if 0 != int(dic[innerKey],16):
                        pos = innerKey.find('id_')
                        id = innerKey[pos+len('id_'):].upper()
                        # 只保留Flash品质测试用例及报告汇总.xlsx指定的SMART字段
                        if id in PublicFuc.flashSummarySmartKey and id in PublicFuc.commonSmartKey:
                            smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
            if '' != smart:
                smart = smart[:-1]
            newDic[key].append(smart)
            if '' == dic['id_a5'] or '' == dic['id_a6']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
        else:
            newDic[key].append('')
            newDic[key].append('')

    return newDic

def ProImtCase(curpath, worksheet):
    #仿真监控测试
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E','T','L','M','N','O','P','S']
    imtKeyLst = ['pc_no', 'Cap', 'qa_err_msg', 'Seq_1M_OverWrite_168H_Iops', 'Seq_1M_OverWrite_168H_MiBps']
    pattern = '.+\\\\Plan34\\\\T-SS-SS-C09\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, '', 1,4)
    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey)
    newKey = imtKeyLst+smartKeyNew
    startLine = 262
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)

def ProBitCommon(curpath, ws, pattern, bitCol, startLine, diskCnt = 4):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1F2', 'SmartInfo', 'A5-A6']
    bitKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration']
    bitDic = {}
    newKey = bitKey+smartKey
    PublicFuc.ReadQaIniData(curpath, pattern, bitDic, newKey, '', 1, diskCnt)
    newDic = PublicFuc.GetNewBitDic(bitDic, len(bitKey), smartKey)
    newKey = bitKey+smartKeyNew
    PublicFuc.WriteData(ws, startLine, newDic, bitCol, newKey)

def ProBitCase(curpath, ws):
    bitCol = ['C','B','E','U','H','J','I','K','T']
    #bit高温老化逻辑盘
    pattern = '.+\\\\Plan59\\\\T-SS-SS-B08\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 55
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #bit高温老化物理盘
    pattern = '.+\\\\Plan60\\\\T-SS-SS-B09\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 63
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #bit低温老化逻辑盘
    pattern = '.+\\\\Plan61\\\\T-SS-SS-B10\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 95
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #bit低温老化物理盘
    pattern = '.+\\\\Plan62\\\\T-SS-SS-B11\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 103
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #plan38bit老化
    pattern = '.+\\\\Plan38\\\\T-SS-SS-B05\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 115
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #写%x容量后老化
    bitCol = ['C','B','E','R','H','K','I','M','Q']
    pattern = '.+\\\\Plan44\\\\T-SS-SS-C13\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 198
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    pattern = '.+\\\\Plan45\\\\T-SS-SS-C14\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 214
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    pattern = '.+\\\\Plan46\\\\T-SS-SS-C32\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 206
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    pattern = '.+\\\\Plan47\\\\T-SS-SS-C33\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 222
    ProBitCommon(curpath, ws, pattern, bitCol, startLine, 4)
    #SLC老化+满盘老化
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1F2', 'SmartInfo', 'A5-A6']
    bitKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration']
    slcBitDic = {}
    pattern = '.+\\\\Plan48\\\\T-SS-SS-C34\\\\BurnInTest_Slc\\\\\d{14}\\\\report.ini$'
    newKey = bitKey+smartKey
    PublicFuc.ReadQaIniData(curpath, pattern, slcBitDic, newKey, '', 1, 0)
    slcBitDic = PublicFuc.GetNewBitDic(slcBitDic, len(bitKey), smartKey)

    fullBitDic = {}
    pattern = '.+\\\\Plan48\\\\T-SS-SS-C34\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, fullBitDic, newKey, '', 1, 0)
    fullBitDic = PublicFuc.GetNewBitDic(fullBitDic, len(bitKey), smartKey)

    bitDic = {}
    for key in slcBitDic:
        if 4 == len(bitDic):
            break
        if key not in fullBitDic:
            continue
        slcRecord = slcBitDic[key][0]
        fullRecord = fullBitDic[key][0]
        slcRecord[3] += fullRecord[3]
        bitRecord = slcRecord[:6]+fullRecord[3:]
        bitDic[key] = []
        bitDic[key].append(bitRecord)

    newKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration','F1F2','BitCycle','Duration','F1F2','SmartInfo', 'A5-A6']
    bitCol = ['C','B','E','T','H','J','I','K','N','L','O','S']
    startLine = 230
    PublicFuc.WriteData(ws, startLine, bitDic, bitCol, newKey)