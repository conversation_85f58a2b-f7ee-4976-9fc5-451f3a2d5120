matplotlib-3.3.2-py3.7-nspkg.pth,sha256=g9pwhlfLQRispACfr-Zaah4Psceyhyx9K_qv929IpMo,570
pylab.py,sha256=Ni2YJ31pBmyfkWr5WyTFmS1qM40JuEeKrJhYKWbd6KY,93
matplotlib/__init__.py,sha256=nIxcNK_DSZOgT5aHHbanHrmTZ-zGeoG-tv0hTslODgU,51946
matplotlib/_animation_data.py,sha256=VBdNJx3GOBmEPjZohLW5uQlNKl90R0j1ojXO7EI0Gvc,7991
matplotlib/_cm.py,sha256=UhH08HifUQWul3EhJfiuqv2lkWtLSLDDWLU28sC77SI,68000
matplotlib/_cm_listed.py,sha256=hA_9d8M187heFixIHIY9ywETb7eIOr9Ei4cDOx1y_pc,111533
matplotlib/_color_data.py,sha256=ZnJq9AKcEl_cviGVCk5f4K6fT0nYB-iv-1k8xiNCOj4,36094
matplotlib/_constrained_layout.py,sha256=WLLx5fVfh7WPGkji0XpgdEjWwCKbatjrsO7m2rFimAk,28080
matplotlib/_contour.cp37-win_amd64.pyd,sha256=5X4GaeKV0ukQHfGfLqLPDmXF6E4sqgoAa9DiFdCTSjI,66048
matplotlib/_image.cp37-win_amd64.pyd,sha256=z6tnTZiPHgzDl4u4vZ2XY4Pze5YUHIWksHCux8bid6Y,190464
matplotlib/_internal_utils.py,sha256=MI2ymzqrQ1IH2yy6-n9mtm765vzgGSdEx7myejgbzt4,2204
matplotlib/_layoutbox.py,sha256=5fdldA1l9qkkvHDbOA0-8hOGDLfokfEm6YCG-R8_N1w,24329
matplotlib/_mathtext_data.py,sha256=HPbLPHO8Fr2ALu_7bx-ZdpVtV5QS4Lqhhwad27LCJm4,57820
matplotlib/_path.cp37-win_amd64.pyd,sha256=drYVxGqRdNYFI4A1psYWoPCg2Rs_PG2N0k6YnU42Wdg,166912
matplotlib/_pylab_helpers.py,sha256=u_A2kpsSrzzHQPZJfrLy_OnoT8ZnERR2PNP5vR-tFHc,4641
matplotlib/_qhull.cp37-win_amd64.pyd,sha256=mgkTk-5MOrM9unNQcKcasd1SBI3YwSaya-inzK7fPTI,414208
matplotlib/_text_layout.py,sha256=VWUKACJHQDffzTPDZDbOuSbrc05tVWxo1FgyzBKh-WI,1074
matplotlib/_tri.cp37-win_amd64.pyd,sha256=rjvFv7ghS9U6J3a8aJ9wIsAqidr7kPtxQoQduUVP58Q,97280
matplotlib/_ttconv.cp37-win_amd64.pyd,sha256=XUwp4hgjv6aM4l4p9whBG6MGYg85-5qdy7xTvosxvGw,64000
matplotlib/_version.py,sha256=WJ_oZxE8b3IVAW0mlwjXTUofXtcKFkt-zBpxF-eEwPI,492
matplotlib/afm.py,sha256=XdHzjPPuOS0nyqggqn34gMtVuRRAG7QKx6bFaMt7x9s,17105
matplotlib/animation.py,sha256=nuxILluQSmMikEb17q00B_YPeT7TX8TTzB7--mHWUyU,68982
matplotlib/artist.py,sha256=sD_FjY3598zk2IRVtYnLoUaCRi4FOQE9HtCqzSK2oww,55635
matplotlib/axis.py,sha256=s6O_vHWKGVIIDfNTxgz6UoGGvOjJHMLweyir699ZpII,94935
matplotlib/backend_bases.py,sha256=loykn9lQUPhO8QqxhoD5WYvHgWBzzRlpt2bjGJWHqEc,130875
matplotlib/backend_managers.py,sha256=jeG3MrkovNCz7GZshQWKmHJx0B6cWlx_nX9Giq8VPBU,14303
matplotlib/backend_tools.py,sha256=F83TpsNO3IHHQyzkgrG2D0nDsSCb5CPM8hZdsLYT32c,36037
matplotlib/bezier.py,sha256=lY6ZggspGISUWtHcgQ0FViih5NicvCeij6Uai0rSN8c,20094
matplotlib/blocking_input.py,sha256=94vk5DbqlDiXbMRRvN8XwdsCPwv26h_Hd_2YEAO6mdQ,11657
matplotlib/category.py,sha256=xm5YGFw4Md1HldRFmBv39XmS4UTVX3iIhVrF2DOMkD4,7392
matplotlib/cm.py,sha256=HIcWq9v_Szs5HpocwBN6-UD5fweBxxHYN0xZPE6XhG8,17227
matplotlib/collections.py,sha256=bc5s7JGcmjtZfjF-_Cr4llq2We5IaTgcL50TWn2SJDs,79077
matplotlib/colorbar.py,sha256=373Lgskpxq1aWbNNM3wtUAk0-OL4MuSZ_AeCwiDt4ng,67375
matplotlib/colors.py,sha256=TnS5YJn_Q6prkRQ24Kltxa-6J3WLwNiokWwxpNmKunc,80447
matplotlib/container.py,sha256=QJuTObESj229OcUGUYeD0DLxG_ssk6cAdfVymk_crwk,4440
matplotlib/contour.py,sha256=Nmx3BG7pF6-u-lmNZdh13BdOlmpJWbohnNRvvczpDRo,70901
matplotlib/dates.py,sha256=Qyv_FbS6VX5lRmeQ_dKzdJch8b0q5b2ptI4qVzaHeRA,69193
matplotlib/docstring.py,sha256=ya7wa0PWtxKSw4KDSkF7dgI8dQq3QLGx7_Exkg4HR_4,2516
matplotlib/dviread.py,sha256=UJ2T2_lzNG7b4TcnD1Aro841NmLTDQt1H32Tuzv_1qQ,41351
matplotlib/figure.py,sha256=V37mwC3NOoi_JNEGnNDkQ-YV6hxOrxbNAWrhlC9Vs38,108822
matplotlib/font_manager.py,sha256=5ut9rsHGXJKj6QATPrAA3obiFVywZoEm3RSMRfDcBBE,50225
matplotlib/fontconfig_pattern.py,sha256=J5-yG_qyi4ACRcDVw7aopFv3raii8QC4DhRPnhLkb5Q,6860
matplotlib/ft2font.cp37-win_amd64.pyd,sha256=Dlfm-tdtuqOOwzb03vDu_F7volkKwRHtqNuTkmfC-Tc,609792
matplotlib/gridspec.py,sha256=2ikhEOv4bo_l7sGHgw946qaRr1VsRZiaXoRHIIxbUns,33890
matplotlib/hatch.py,sha256=6CvCCNXRK5rOfm5QXOZPOw3sqy2exoMJcgF9CrFBmu4,7098
matplotlib/image.py,sha256=kat4dB1TGQGs9J5D11vozK40lMp754HdVxm1hG6JEx8,68443
matplotlib/legend.py,sha256=5nVA1rQ0O1ocZcdwHab-SDZSkdQpfOQVj7-Owzws1-w,48832
matplotlib/legend_handler.py,sha256=LkImW6-iq3Mmdg8HhO332bPRG52CyF0PzJFJC4GURM0,26940
matplotlib/lines.py,sha256=fe1BMCt7eIjzxhFJMKfa1cTeUOjgHKXIcp4vtZLBlGU,53073
matplotlib/markers.py,sha256=2uZMUiLqcllco5wDagOR6KTrcC1xFktXvKAHPA3IY1E,32895
matplotlib/mathtext.py,sha256=kpI5WS3lOv87sxezsfq1_oU-IyxMMzgC2M-HsijHLu8,123994
matplotlib/mlab.py,sha256=c-Z_VxzBCq70gpS_CeAK41krVKWsAbogdD_WDwKNAdc,36780
matplotlib/offsetbox.py,sha256=zdr7NNI2l9Er6G1i7OgH2ZhrwbTpQO10yI-ZqpmbIsk,61753
matplotlib/patches.py,sha256=-sEU_HwmsPle-Vo_OlfQrYEoswovpqpDi1AigE5z2Q4,153320
matplotlib/path.py,sha256=SdnGJGVPVIiBJALUR7UpNRNO0qNFjT05dKp2w2PiPWE,40487
matplotlib/patheffects.py,sha256=sFJ-GfsPwdF-eS5273bgseShb01-hFkTEhdbtIHkGig,13975
matplotlib/pylab.py,sha256=NUr7XkQQSQwKS-l1z1ur54YcZJLN_mgbtsagAuPKz68,1742
matplotlib/pyplot.py,sha256=1hcTG7XEIjzfDQRkSd3KnDZVItIVj6wAT9TNIP_DIW0,119057
matplotlib/quiver.py,sha256=cprVfYoP0UEglSroDqfqukjj4lwYtdDavyZfyF46S_s,48503
matplotlib/rcsetup.py,sha256=0Aj8RaT2e_TOCxRu5A9Th7VuNTXh7iEXwuTtS1TZBfU,58761
matplotlib/sankey.py,sha256=U5F9LUBI9GoC4PyHJg11Ow7pmZIbwqiX9-CPANH-V5g,37159
matplotlib/scale.py,sha256=Aiy1JJpllbTg5mlFWmut7Q5GWRLrg-8B7OIMukrWlWU,24311
matplotlib/spines.py,sha256=gyEYiuxVnXx6ewwqiB4j-L4hkcoQqifL5R_-5okWowM,20759
matplotlib/stackplot.py,sha256=KaB8W5_SVSQeN4bVXhh5wnyu61ZEKJPV5ISOpWIccao,4025
matplotlib/streamplot.py,sha256=qEVUKdQqgEIO8PGeEEjA_NVLISA09P0G3YPNlPhmOtA,23619
matplotlib/table.py,sha256=9GTl9lNFQX7RF0SrC5gaO0CBHFnVFzLhoVCWNPQ2EwY,27382
matplotlib/texmanager.py,sha256=yDycQwJ014_j6SaZiHWe0tDEhDHhrY7QA-07xiSsn-c,16295
matplotlib/text.py,sha256=YiUOb5wY1tf1gh8r4w2Y2cE3MIj6M24Sa2drtyQAGOE,67943
matplotlib/textpath.py,sha256=U0SSfiAb6CFPAp0eLWj4jR5-lCEpLRdtyWSZzrFlkB0,15421
matplotlib/ticker.py,sha256=XL31WXTR5NiIoW_o9YPMkhB_sN80BVMKmoLQfKzGk64,107299
matplotlib/tight_bbox.py,sha256=3nPRkvIogLL6n-ZJPPRsOLCC_cZBEmgKAsJfvhC9xQk,3023
matplotlib/tight_layout.py,sha256=wv_8EjQTMhXDCSTjFZTBOmzvUKAVuYt1q1LDqiyk72s,13494
matplotlib/transforms.py,sha256=guwKIhVDmgbxtf86msod0KZhMvWoSbcAnQ663a9_Wqw,99721
matplotlib/ttconv.py,sha256=qZFAggBaXK9jMpA5YkRb2G7AyK9avcuxA6o4VbQLUKM,248
matplotlib/type1font.py,sha256=bbwW3TLCfXUTilJmWXFE_KtnoJNrtw-M7e9LHUiW5Cw,12663
matplotlib/units.py,sha256=HpMVm8uMp4JiL3wJDMDOH0MiqxVLRUy6WD2Iizs8BLU,7497
matplotlib/widgets.py,sha256=fj5fyhbA4E0OojTRvBubazBoNkgy31PDbyumBxOtmBI,95922
matplotlib/axes/__init__.py,sha256=5LED7GJ0OxIdJfsHtnC1IYjxF9SNkyQg7UpmCkBT76k,48
matplotlib/axes/_axes.py,sha256=0zBbRvKDo-yRMtKm4CvRpTS2xQ7W1YHSE1AijJeLHAM,321258
matplotlib/axes/_base.py,sha256=ZJKTPFPvMmuNWNwOT2wv3AgXu605ZufZNw4cCCFKtqM,163369
matplotlib/axes/_secondary_axes.py,sha256=NySpuooPhdX0QfBL698jg0jEkToTBW21YXP5DDbut0Q,13746
matplotlib/axes/_subplots.py,sha256=JSpGTJYOy_XQygVwdBNlm_1ZvXhlSmsF42nUHUn9mV4,9900
matplotlib/backends/__init__.py,sha256=ASrypuHdJgwLQwfr7X9ou0hlJohw_7V4t8CmpazOY7I,109
matplotlib/backends/_backend_agg.cp37-win_amd64.pyd,sha256=GwSlRI-g7mE1P9CYJzhgRbUItluZNUY_VglAcFOVPoI,247296
matplotlib/backends/_backend_pdf_ps.py,sha256=4VlQejw7QYo8p0TGvemnZ-YrN_NUj0goRGkzYJRvooM,3895
matplotlib/backends/_backend_tk.py,sha256=K0RrP8cPd8lE4auSpxIr1ty8xfPeZWw4t1y6hLhFJv0,33775
matplotlib/backends/_tkagg.cp37-win_amd64.pyd,sha256=SGRcD37X7erlay_N4GIZZxoxlFo-EK3K2T94QQmjJYE,25600
matplotlib/backends/backend_agg.py,sha256=JkmOGb7mEGju_m08VSygQv_J-fl8okg5sbkUDlbRrkg,23621
matplotlib/backends/backend_cairo.py,sha256=r03YcTMYdwJV1UbtHcd4xcgVhdHi3x_exaZc3pJFzPc,19219
matplotlib/backends/backend_gtk3.py,sha256=y5sIGimTq7fKI2LYaxY0OETsAn8NXADye9yuNM5UT0U,34074
matplotlib/backends/backend_gtk3agg.py,sha256=CZynWriGhHAZfcqDQcwJ3b0ZMgn-Lj6ZVHBDgs0OcUo,2890
matplotlib/backends/backend_gtk3cairo.py,sha256=Jv4_AoYuQxm-E_ycTw_WZY24Sl40dXGqgM_AbcvfLt0,1391
matplotlib/backends/backend_macosx.py,sha256=rgqh_ULdJrbqzYyAkKDvxZ5iMnvHhuKHA0SxZO63QoI,5849
matplotlib/backends/backend_mixed.py,sha256=l27mVBO_YjJi8x1Qzy4qrGy68lSdOv1U74peIymhjv0,5323
matplotlib/backends/backend_nbagg.py,sha256=2n9tvHQMlumnq6RhrJpY8pPxMDQ3W6I186d5D4G5pFE,8814
matplotlib/backends/backend_pdf.py,sha256=vpvhoxBZ19g9FWf--XL3j4WsfqqmTe39EZGuG4DMieA,99623
matplotlib/backends/backend_pgf.py,sha256=3vP15NPoJS8H8TMsj5xM-YJ8j9PCdLGRvLsCk0J5UYs,44633
matplotlib/backends/backend_ps.py,sha256=Dya3uEPWB23cXJo4zw48Wzto7XzghkfO_cXD3sBBRmI,48079
matplotlib/backends/backend_qt4.py,sha256=jfPKB0_c44yxOEtFVyCQF278b0FcP3mzVwFqx9hjvwI,528
matplotlib/backends/backend_qt4agg.py,sha256=i6tgNDEB2Wn8-8VAzoMOIwnprk8tWtw7hF6aJgijFqs,395
matplotlib/backends/backend_qt4cairo.py,sha256=GI-7aWPfK3m-g8SboesEnX9xbJKjGf235kG-ZjEjojE,327
matplotlib/backends/backend_qt5.py,sha256=qUPBeWmTV_-NRsTYyswY7k4TQrZ4ibAcc2kw_88eNHE,40269
matplotlib/backends/backend_qt5agg.py,sha256=csXMx4VBEEhoaSHlTyyZs4haGJK1a4NnTx9ROtwzAro,3247
matplotlib/backends/backend_qt5cairo.py,sha256=3Nw5QyHNOVF4HZ5L7VRodePqjVjPeOSIm9xjQJSbP_U,1865
matplotlib/backends/backend_svg.py,sha256=9Rc3YiBycRIqU9wOGPDQoZ4L1-WtjTs4_T1jhru47gg,50534
matplotlib/backends/backend_template.py,sha256=pmoNUPWnZ239xqdD3513o1aFT6mX2S-6azoqiqTIaYI,8674
matplotlib/backends/backend_tkagg.py,sha256=D_GuTCki9bCjRvsTIrwH5KBqR0t3YS9dHDUVWpZvPf8,697
matplotlib/backends/backend_tkcairo.py,sha256=9Qw6AlLvt92blm9-J9fIp5-Rsh0NmvHZtZNIiT6lxzI,1100
matplotlib/backends/backend_webagg.py,sha256=DQgTUyabZ295QJixlbamxa2Vfa9feKUW782d_DDD0cc,11384
matplotlib/backends/backend_webagg_core.py,sha256=X25yA6B1M3umY0HTos8Q4_HWgGgeumQaJh9FY_lU7Wo,18565
matplotlib/backends/backend_wx.py,sha256=QSvZ4BXvgyGPyS387WyxlGkiDT2ieR6qOh740_C9Dr4,62340
matplotlib/backends/backend_wxagg.py,sha256=je8nTv--IruEOs0KgiJOfl1sEo7wBbkkquqiPFaUKOo,3024
matplotlib/backends/backend_wxcairo.py,sha256=fsDGrdOKng2qGlN1gOHa6_6UgfNiXWUpu_CO_A67fqc,1880
matplotlib/backends/qt_compat.py,sha256=xt1i9q0WGqd3vIiSPFLn0f7VBHkX6WmjhyG3qg_bVc8,8289
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/_formlayout.py,sha256=3YMhRLpmiPygAp69FDzNNNE7tMft5Idq6uBdWj4PDUE,21150
matplotlib/backends/qt_editor/_formsubplottool.py,sha256=IJRLiHJYNay9dq8soSR30RpzsDy2QGK_VM-de-skcMM,1547
matplotlib/backends/qt_editor/figureoptions.py,sha256=Bv1fpNcBEGJZNaqaJ8fEfZThl4cxxlnlMAPl5HBpE6g,9731
matplotlib/backends/qt_editor/formsubplottool.py,sha256=duxTyAhD8XFzgZ9Fnw6a4ZM34QDDEZweabXE4up2jPg,244
matplotlib/backends/web_backend/.eslintrc.js,sha256=Dv3YGyMCOxbDobwrxr332zNYMCxb6s_o07kQeizIko8,698
matplotlib/backends/web_backend/.prettierignore,sha256=fhFE5YEVNHXvenOGu5fVvhzhGEMjutAocXz36mDB0iw,104
matplotlib/backends/web_backend/.prettierrc,sha256=Yz-e2yrtBxjx8MeDh7Z55idCjKgOxGZwSe6PQJo-4z0,156
matplotlib/backends/web_backend/all_figures.html,sha256=4iWdKDVq2wj-ox_wGB6jT_5a1XIZfx7cCSxptQr11_U,1669
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=C4mEsVfrNuy5K92pzLtrgw0wP_XG_-2h74CTngsPvCQ,1345
matplotlib/backends/web_backend/nbagg_uat.ipynb,sha256=m_Bmbn9FfF1jUCvk_7C_x_Ho6uzvj7KRayRhNsehIgQ,16572
matplotlib/backends/web_backend/package.json,sha256=01wvCHHnw0mTVKiKLo3WHOtd5DxM6dBuHfO-xXpc7yw,493
matplotlib/backends/web_backend/single_figure.html,sha256=Tv4FxHdVi872xGsC_WHeOsR9zCUmWNR4KoqEur9nTJ4,1276
matplotlib/backends/web_backend/css/boilerplate.css,sha256=y2DbHYWFOmDcKhmUwACIwgZdL8mRqldKiQfABqlrCtA,2387
matplotlib/backends/web_backend/css/fbm.css,sha256=-5wOcfCz-3RLDtEhOPo855AyDpxEnhN6hjeKuQi7ALE,1570
matplotlib/backends/web_backend/css/mpl.css,sha256=VfGbqCCnb-3ZCSgUyv1PcmfEPvank9340v-F2oyhapw,1695
matplotlib/backends/web_backend/css/page.css,sha256=qCCXiXJvwyM3zKpOlrhndn2kZl0CpOcd2ZDXA4JlLwo,1707
matplotlib/backends/web_backend/js/mpl.js,sha256=NNW31FIjoKLrtvucppMZ5qON3n8oowAN91Q1yW28Tx0,21583
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=k3JjkEWP-C2DCk8qrA3JPXFRl8Xu6gCf7zMtBZO4s5c,310
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=UJ_93YaMoP1wv1cUJOEOkJX9ybl55MMYsA0UazFDMfU,9057
matplotlib/cbook/__init__.py,sha256=x57PztycDvnYq5w0IYu4hBWSbkduJxGfJY-38I1ukN4,79579
matplotlib/cbook/deprecation.py,sha256=5KlE9p5XReOGJQUk7-3J79za0exp1KxkZSr-On5AIYI,19409
matplotlib/compat/__init__.py,sha256=jbHKhpHmaPogoOMoo6GENDl6oHffkq6GMgR9Nwpo0HY,98
matplotlib/mpl-data/matplotlibrc,sha256=Qo18I-IbXQawZIgtVzbeQ0nx_yalJjnyjrCr0Q8yp1Q,40658
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=zdDttyyqQ6Aa5AMVWowpNWgEksTX5KUAFqUjFMTBiUc,12290
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=dCq-QWC9Vl4WmcD9IOi-CMMB-vmVj8VBTOJS20ODMC0,10742
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=bGb6XAS-H48vh-vmvrF0lMunP1c-RGNB3Uzm1Am9vew,10444
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=lxhR0CjcTVxKsQ4GPe8ypC4DdYCbn5j4IXlq2QTAcDM,8490
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=kFzBQ0WX0GZBss0jl_MogJ7ZvECCE8MnLpX58IFRUFU,6657
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=_-81-K4IGcnEXZmOqkIMyL42gBRcxMEuk8N8onDtLIM,17759
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=GrsbRiN4fuoPK-SiaMngnmi5KyZC_nOFf_LYFk_Luxg,17831
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=Fjz-OUzE9qB-MCosDuUrBnMq8BXmldx6j_zgj2mKc1k,17814
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=pS716alw6ytmYYSRnz6hPvb1BZPlq3aUiViJFYagsHk,17919
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=WOJW5hnEnwBQGQsVxtdXI2PJ1m8qwF-xC4n6aD3-hRI,15572
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=6D2SRhcYc-apq_Qq62bj_FwU_uHxIK09Tt_wjSSJS7c,15695
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=_ZJuFBKoz70E-SBlhG4Tb9Yqxm3I6D-jALpy-R-vcew,15407
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=1_6b55YPDXk9a3RUnNvja7y2iR3P6BKnh6DvkPnZ5pA,15591
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=5CDJe3t71aM5qinDYSE8svWhc6RjteZPNslOvNVp8NA,15696
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=jNfbbBHfwvu0RglxuLeuh1K10KWEkj1lEVJR_hXQSWE,15766
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=Hx4X9kbsRm_S3eo9WLjuOQzuOmeZHO33b6uhDTsH1NQ,15683
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=DLrBm4iOvjCpKBnyC7yGAO4fdIvRUCO0uV_kQiy9VfQ,15787
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=PR_ybr2HVx6aOXVRza7a-255AtGEyDwj_pC_xK1VH1o,17725
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=pFHdjRgEoKxxmlf1PcTc-3Hyzh1Fzz2Xe2A8KzT3JuA,17656
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=4ocMbnfWYxd-YhpzbWPDRzDdckBRIlEHPZfAORFiaZQ,17800
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=cOAehWQUbLAtfhcWjTgZc8aLvKo_cWom0JdqmKDPsTo,17765
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=QTqJU4cVVtbvZhqGXFAMRNyZxlJtmq6HE6UIbh6vLYE,16072
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=fAEd2GRQzamnndBw4ARWkJNKIBgmW24Jvo4wZDUVPRg,16174
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=7G6gNk10zsb_wkQ2qov_SuIMtspNafAGppFQ9V-7Fmo,18451
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=9TCWRgRyCgpwpKiF98j10hq9mHjdGv09aU96mcgfx2k,18393
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=9eXW8tsJO-8iw98HCd9H7sIbG5d3fQ-ik5-XXXMkm-8,18531
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Rgr4U-gChgMcWU5VyFMwPg2gGXB5D9DhrbtYtWb7jSE,18489
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=ZAfYR6gDZoTjPw1X9CKXAKhdGZzgSlDfdmxFIAaNMP0,16500
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=h6gIWhFKh3aiUMuA4QPCKN39ja1CJWDnagz-rLJWeJA,18098
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=wqphv_7-oIEDrGhzQspyDeFD07jzAs5uaSbLnrZ53q0,17189
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=mXZEWq-pTgsOG8_Nx5F52DMF8RB63RzAVOnH3QcRWPo,17456
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=Yd8M-qXEemyVsBt4OY7vKYqr7Yc_KfRnb2E505ij3Ts,16096
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=BLReUiSSOvoiaTymK8hwqWCR2ndXJR2U2FAU2YKVhgM,16251
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=OdM7mp--HfFWfp9IwolGoviuHphoATNFl88OG3h3Uw8,16197
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=n0_vo-JC8voK6FKHvnZCygzsvTfNllQRya3L0dtTRZY,16172
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=HItpBqCppGKaLaLUdijTZ31jzUV13UVEohYVUPSk1Kc,9853
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=td8VINDw_7_X3U6dHLcNxT-_2wU_CqaTSntSW696-M8,18631
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=ZbK1H28xxIwcZGSqGR6iVtpMrZ15LRN1OfVLpt1yiZ8,18718
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=lwDiLF8RkJ46RoVFY0qEQ9J_h54cQHF6MCRx3ehLG_Y,18590
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=rYNz084EPuCgdbZcIvuaXA77ozg5sOgmGZjwpFzDGKQ,18716
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=RJuuhzr-dyocKMX4pgC9UoK3Ive2bnWtk74Oyx7ZBPk,22537
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=1gyQknXqpiVmyKrcRhmenSJrP0kot5h84HmWtDQ3Leg,22948
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=Y_v97ZJKRPfPI2cFuFstBtBxRbAf3AK-hHYDVswMtdA,23177
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=Hxu2gSVpV93zKO6ecqtZSJZHQyE1xhwcUnh-RJ8TYPo,22899
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=BhzLcod8-nVd2MWeZsO_GoZUXso4OhQktIZ2e7txJY8,16730
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=a7-NgSTSEyakts84h-hXLrbRrxmFhxr_NR51bhOLZYQ,9689
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=rQFQ1L7cyId3Qr-UJR_OwT40jdWZ1GA_Z52SAn0ebpk,15675
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=y4LmnvX21CHo9AT-ALsNmTQlqueXJTMdDr-EfpTpfpI,15741
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=snEDsqLvYDDBEGJll-KrR7uCeQdaA5q5Fw-ssKofcOE,15783
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Uh4NfHUh79S-eKWpxTmOTGfQdx45YRWwNGvE73StpT0,15677
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=uIDZa69W0MwFnyWPYLTXZO9JtVWrnbKUuVnAAW3uQfI,72096
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=aZhKNcomlzo58mHPg-DTZ-ouZRfFkLCwMNTUohnZwmk,72192
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=tGCbcbZgo5KsCd81BgJxqHbCzmZhfdg7-Cb3QrudlyE,77443
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=2jPxhwR0yOaL_j4jU_8QerbG7qH5g2ziqvHhoHsXmC8,77343
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=PSEoqCA3WhDem8i_bPsV3tSCwByg-VzAsyd_N-yL3mY,9953
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=tKAA7YXLIsbN2YWqD9P2947VB5t8aGDcTwI04NDjxSI,66839
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=k8R0S6lVIV3gLEquC3dxM0Qq2i96taKvMKBAt5KzxV0,62026
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=7Tf6LmpntbF9_Ufzb8fpDfMokaRAiGDbyNTLvplZ4kI,68995
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=do4cq-oIXUiaY9o-gLlrxavw7JjTBzybTWunbnvMumQ,62879
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=oyVlyQr9G1enAI_FZ7eNlc8cIq3_XghglNZm2IsDmFk,9752
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=yQ1iD9441TPPu5-v-4nng62AUWpOPwW1M_NoeYTwGYQ,843
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=xhup6GaKURy9C8_e6DKeAspspASKvabKfuisaKBZd2o,4915
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=LVswXqq9O9oRWEuSeKQwviEY8mU7yjTzd4SS_6gFyhY,5599
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back.gif,sha256=sdkxFRAh-Mgs44DTvruO5OxcI3Av9CS1g5MqMA_DDkQ,608
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back_large.gif,sha256=tqCtecrxNrPuDCUj7FGs8UXWftljKcwgp5cSBBhXwiQ,799
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave.gif,sha256=wAyNwOPd9c-EIPwcUAlqHSfLmxq167nhDVppOWPy9UA,723
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave_large.gif,sha256=IXrenlwu3wwO8WTRvxHt_q62NF6ZWyqk3jZhm6GE-G8,1498
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward.gif,sha256=VNL9R-dECOX7wUAYPtU_DWn5hwi3SwLR17DhmBvUIxE,590
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward_large.gif,sha256=H6Jbcc7qJwHJAE294YqI5Bm-5irofX40cKRvYdrG_Ig,786
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.gif,sha256=3lRfmAqQU7A2t1YXXsB9IbwzK7FaRh-IZO84D5-xCrw,1267
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=tsVIES_nINrAbH4FqdsCGOx0SVE37vcofSYBhnnaOP0,4888
matplotlib/mpl-data/images/hand_large.gif,sha256=H5IHmVTvOqHQb9FZ_7g7AlPt9gv-zRq0L5_Q9B7OuvU,973
matplotlib/mpl-data/images/help-symbolic.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help.gif,sha256=3Cjr7YqfH7HFmYCmrJKxnoLPkbUfUcxQOW7RI2-4Cpo,564
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.ppm,sha256=mVPvgwcddzCM-nGZd8Lnl_CorzDkRIXQE17b7qo8vlU,1741
matplotlib/mpl-data/images/help.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/help_large.ppm,sha256=MiCSKp1Su88FXOi9MTtkQDA2srwbX3w5navi6cneAi4,6925
matplotlib/mpl-data/images/home-symbolic.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home.gif,sha256=NKuFM7tTtFngdfsOpJ4AxYTL8PYS5GWKAoiJjBMwLlU,666
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home_large.gif,sha256=k86PJCgED46sCFkOlUYHA0s5U7OjRsc517bpAtU2JSw,1422
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_128.ppm,sha256=IHPRWXpLFRq3Vb7UjiCkFrN_N86lSPcfrEGunST08d8,49167
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move.gif,sha256=FN52MptH4FZiwmV2rQgYCO2FvO3m5LtqYv8jk6Xbeyk,679
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move_large.gif,sha256=RMIAr-G9OOY7vWC04oN6qv5TAHJxhQGhLsw_bNsvWbg,951
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=E00YoX7u4NrxMHm_L1TM8PDJ88bX5qRdCrO-Uj59CEA,1244
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots.gif,sha256=QfhmUdcrko08-WtrzCJUjrVFDTvUZCJEXpARNtzEwkg,691
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots_large.gif,sha256=Ff3ERmtVAaGP9i1QGUNnIIKac6LGuSW2Qf4DrockZSI,1350
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect.gif,sha256=mTX6h9fh2W9zmvUYqeibK0TZ7qIMKOB1nAXMpD_jDys,696
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect_large.gif,sha256=nx5LUpTAH6ZynM3ZfZDS-wR87jbMUsUnyQ27NGkV0_c,1456
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/None_vs_nearest-pdf.png,sha256=5CPvcG3SDNfOXx39CMKHCNS9JKZ-fmOUwIfpppNXsQ0,106228
matplotlib/mpl-data/sample_data/README.txt,sha256=c8JfhUG72jHZj6SyS0hWvlXEtWUJbjRNfMZlA85SWIo,130
matplotlib/mpl-data/sample_data/aapl.npz,sha256=GssVYka_EccteiXbNRJJ5GsuqU7G8F597qX7srYXZsw,107503
matplotlib/mpl-data/sample_data/ada.png,sha256=X1hjJK1_1Nc8DN-EEhey3G7Sq8jBwQDKNSl4cCAE0uY,308313
matplotlib/mpl-data/sample_data/ct.raw.gz,sha256=LDvvgH-mycRQF2D29-w5MW94ZI0opvwKUoFI8euNpMk,256159
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=IG7mazfIlEyJnqIcZrKBEhjitrI3Wv35uVFVV6hBgMo,143
matplotlib/mpl-data/sample_data/demodata.csv,sha256=jswIPVUGO-gMkpzyA73BzR3rpwUabVl30SXVrMzZvx0,670
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=IcJ5PddMI2wSxlUGUUv3He3bsmGaRfBp9ZwEQz5QTdo,2250
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/grace_hopper.png,sha256=MCf0ju2kpC40srQ0xw4HEyOoKhLL4khP3jHfU9_dR7s,628280
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=ITxkJUsan2oqXgJDy6DJvwJ4aHviKeWGnxPkTjXUt7A,33541
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=4JtKT5me60-GNMUoCMuIDAYAIpylT_EroyBbGh0yi_U,3276
matplotlib/mpl-data/sample_data/percent_bachelors_degrees_women_usa.csv,sha256=Abap-NFjqwp1ELGNYCoTL4S5vRniAzM5R3ixgEFFpTU,5723
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=uU84qox3o_tHASXoKLR6nBJmJ9AS0u7TWXxTFZx9tjA,1308
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=9XRyb2XzCtS6piLIYFbNHpU-bF4f7YliWLdbLXvBojI,173
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=UTO__T6YaaUY6u5NjAsBGBsv_AOK45nKi1scf-ORxzU,741
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=HZfPeokxDqwLhhck_tGh8ugTbjMpxqJsW1r_lo0TZis,24713
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=Vei27QYOP3dNTaHzmRYneNLTCw30nE75JOUDYuOjnXc,687
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=HDqa0GATC9GjNeRA8rYiZM-qh7hTxsraeyYziGlbgzg,299
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=IfXwiatqkv6rkauNnjcfDDS6pU-UabtEhbokK5-qAes,872
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=pWh3RqvTy3fyP_aGOa1TR7NMAi5huuWDJRPeZM5kR3o,996
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=MnigXJy2ckyQZuiwb-nCXQ0-0cJBz1WPu-CEJXEHWpA,555
matplotlib/mpl-data/stylelib/seaborn-bright.mplstyle,sha256=DIo92H5LVQVPMeJOcVaOPOovchqMeDvkKoEQ0BX--wA,147
matplotlib/mpl-data/stylelib/seaborn-colorblind.mplstyle,sha256=M7OYVR1choIo_jlDfMsGSADJahLDauZEOUJJpuDK8Hs,151
matplotlib/mpl-data/stylelib/seaborn-dark-palette.mplstyle,sha256=HLb5n5XgW-IQ8b5YcTeIlA1QyHjP7wiNPAHD2syptW4,145
matplotlib/mpl-data/stylelib/seaborn-dark.mplstyle,sha256=IZMc2QEnkTmbOfAr5HIiu6SymcdRbKWSIYGOtprNlDw,697
matplotlib/mpl-data/stylelib/seaborn-darkgrid.mplstyle,sha256=lY9aae1ZeSJ1WyT42fi0lfuQi2t0vwhic8TBEphKA5c,700
matplotlib/mpl-data/stylelib/seaborn-deep.mplstyle,sha256=djxxvf898QicTlmeDHJW5HVjrvHGZEOSIPWgFK0wqpw,145
matplotlib/mpl-data/stylelib/seaborn-muted.mplstyle,sha256=5t2wew5ydrrJraEuuxH918TuAboCzuCVVj4kYq78_LU,146
matplotlib/mpl-data/stylelib/seaborn-notebook.mplstyle,sha256=g0nB6xP2N5VfW31pBa4mRHZU5kLqZLQncj9ExpTuTi8,403
matplotlib/mpl-data/stylelib/seaborn-paper.mplstyle,sha256=StESYj-S2Zv9Cngd5bpFqJVw4oBddpqB3C5qHESmzi8,414
matplotlib/mpl-data/stylelib/seaborn-pastel.mplstyle,sha256=8KO6r5H2jWIophEf7XJVYKyrXSrYGEn2f1F_KXoEEIc,147
matplotlib/mpl-data/stylelib/seaborn-poster.mplstyle,sha256=8xZxeZiSX2npJ-vCqsSsDcc4GeFrXwfrSNu0xXfA2Uk,424
matplotlib/mpl-data/stylelib/seaborn-talk.mplstyle,sha256=_c29c8iDdsCMNVERcHHwD8khIcUVxeuoHI2o1eE0Phg,424
matplotlib/mpl-data/stylelib/seaborn-ticks.mplstyle,sha256=Annui6BdMJqYZsIGCkdmk88r4m_H4esa7bSszkBpm-A,695
matplotlib/mpl-data/stylelib/seaborn-white.mplstyle,sha256=VY6sw8wkqbl0leWtWa5gz8xfDMfqt5yEhITIjP4FsOI,695
matplotlib/mpl-data/stylelib/seaborn-whitegrid.mplstyle,sha256=IOm2H1utXO_zR7FWqMLBiRxHyxABL3kq1fh0-6BDJ0E,694
matplotlib/mpl-data/stylelib/seaborn.mplstyle,sha256=N9lUFHvOn06wT4MODXpVVGQMSueONejeAfCX5UfWrIM,1187
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=PzUMoOtw0V6l0bPk8ApRAKvcxdJmzRU2bVOkNqz8DnU,192
matplotlib/projections/__init__.py,sha256=yaWM60AYTHz3geKwIwnp-dDaAhvaKSlj65uZFDJNe70,1728
matplotlib/projections/geo.py,sha256=_njFHfRBw8RNrej5WcOg3qcstFVaDak6f1MjSl62kXA,17967
matplotlib/projections/polar.py,sha256=iqynlkWG_heAjb3eoLxj-f13y3SjxipzbbwlulsseXo,55707
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/mathmpl.py,sha256=yY0d7NsVcFiujAadP44DMesGp2Zyhz3USYjsznq4WDU,3885
matplotlib/sphinxext/plot_directive.py,sha256=8XX4AJ1Na44_O3btZpOIG9oE9zN0z8q3TOwDhZvwnj8,27024
matplotlib/style/__init__.py,sha256=Lee0QNI5VNg45wfj5h5w5_PhaBOq8zDPrDtqnpJfFWI,68
matplotlib/style/core.py,sha256=PkT0m0NfFi8UjYcL1PLTE7tbYe9pJkuwpDMqBiXdnng,8724
matplotlib/testing/__init__.py,sha256=5A2r174QGv1RzXQovaJzdx4ZuLTtb9lGfEWTRuzncc8,1454
matplotlib/testing/compare.py,sha256=16h0JgcJJTXJ4M5Y3au-Qv8b30Amlmnene61XPdu7ag,17453
matplotlib/testing/conftest.py,sha256=4SsbLZYORUGz9GBDDqVVt-JTVXUe_RO2zfYeZ0f3VQY,5704
matplotlib/testing/decorators.py,sha256=fCXaMvJJ6up0WMpbmpdzXEhW6y72TVEBRnnrLQPwKtU,19219
matplotlib/testing/disable_internet.py,sha256=iJ6yi2K3Irql3VRpB37B6dV8jF8ppR-9bIJ7CSLbiSY,5064
matplotlib/testing/exceptions.py,sha256=rTxMs5B6lKjXH6c53eVH7iVPrG5Ty7wInRSgzNiMKK4,142
matplotlib/testing/widgets.py,sha256=JN-ZY_GWrBYM7-WJl_EnJVB7EwwNUPoueZg2tLveg80,1566
matplotlib/testing/jpl_units/Duration.py,sha256=6hE7fLS4BUeCidjlXrNpk9LJ1Y37KaW-Bgg-uYUZeYo,4623
matplotlib/testing/jpl_units/Epoch.py,sha256=dJL3tvga_kHuW7YYy10PYw5Bw9_XditGf1AQiAw2m6s,6582
matplotlib/testing/jpl_units/EpochConverter.py,sha256=-mQ_6zbsgGvoka6JAnzoFk8V6ggWmlizJl4kgR6fhXI,3264
matplotlib/testing/jpl_units/StrConverter.py,sha256=wewzDhcl1A2kG0MHtwrCGqWqhsUz-TfIxzMSpXbqjro,3053
matplotlib/testing/jpl_units/UnitDbl.py,sha256=CNUbYh_us3Fbf7Eg5O4mWcBXT-zsjfsJ3dGJaemWp98,7873
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=dkLzijhvoXNR-pOwYHGN_TSkpw1yV-88B31Xitzo1ho,3190
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=3ZrRTulxYh-fTRtSpN9gCFqmyMA_ZR2_znkQjy3UCJc,709
matplotlib/testing/jpl_units/__init__.py,sha256=gdnES2cnASttTSfKQn-g400gQ0dDZAQ_Kn09J_HyFJY,2760
matplotlib/tests/__init__.py,sha256=y2ftcuJhePrKnF_GHdqlGPT_SY-rhoASd2m4iyHqpfE,376
matplotlib/tests/conftest.py,sha256=tjbU0uzdD8q4j30uWm-lzYfZCjqFYRAF_-WMhA3O0qY,262
matplotlib/tests/test_afm.py,sha256=afU9PfGYjm_WdfwGvoTfjTLgMxx_Vpn26wCDaWuFrg0,3847
matplotlib/tests/test_agg.py,sha256=WA7gzi_zRxh_jPcmkf8ZO1p_NQry5__qF9GLzK8-obE,7675
matplotlib/tests/test_agg_filter.py,sha256=i8Wv2oXmvi-m6020p_k8C3BCJ9UB6tBVSVNsul5NEaQ,999
matplotlib/tests/test_animation.py,sha256=BS59_1os6n3Dm18njppZrO70Siqufwg2EMn5WUO0M_k,8628
matplotlib/tests/test_arrow_patches.py,sha256=0w0X1vPHRd__DOy2oVrYupG9Rk-EuWzyiet4piB4bF8,5820
matplotlib/tests/test_artist.py,sha256=2wZZaSk04a1BWHcycnd1SSDy_QxKEUCeKS8yOCv-ijQ,9340
matplotlib/tests/test_axes.py,sha256=Wsd__JvtGmvymBNHszuvg3-ZbJo9y2ZXz1AsR-qB9jc,216653
matplotlib/tests/test_backend_bases.py,sha256=wWnNPhja06EFHkK6G7BP6LxsdXV5mvyEnqcWGdXcAQk,6094
matplotlib/tests/test_backend_cairo.py,sha256=2jejOSazlpDheGuSBe2C6R2D37bMjmMy3Ds2NRCzFnA,1869
matplotlib/tests/test_backend_nbagg.py,sha256=rfFHvT0YhzBMdm-t3P-GBRKi-ZWgjTXie_Zuo6pngt0,935
matplotlib/tests/test_backend_pdf.py,sha256=smPXlrcaA1jwSrSbvk7OU5Swx95KpVTZJPiy4jWrAUs,8941
matplotlib/tests/test_backend_pgf.py,sha256=mmZwFn0289xZER-QSspcpP2uloXtb__YJsf4IpRSsrw,10646
matplotlib/tests/test_backend_ps.py,sha256=zTd9xtJ74PN8ayuTCFSHKuzPGtO2WHkNbGkeW3VQzbc,4723
matplotlib/tests/test_backend_qt.py,sha256=Wptn99GISjKld3R_14o3EIWtfUBCWbd1vEjbrGDW_10,9508
matplotlib/tests/test_backend_svg.py,sha256=R8AnTHOc8Rf9AOoyK4pTcBTDwM_CaK3Q_NTPzpVVCJc,12841
matplotlib/tests/test_backend_tk.py,sha256=roCO6jRKhyXYLg_-pA1LFGS77S_f0ZZ9ZAs7GqVsjXA,1443
matplotlib/tests/test_backend_tools.py,sha256=6StNyy5SGVdfh1r-UwXyQnMg_XJCiXL_hC40HehT-zA,521
matplotlib/tests/test_backend_webagg.py,sha256=lQP6u1Vq8eV_9a7AnUzv58ORFdwXC9ggVZq2FfS6nto,729
matplotlib/tests/test_backends_interactive.py,sha256=icuX1A-AKY6AK-JrYQUJX888asmgzPEfe-vBsBOJpz4,8102
matplotlib/tests/test_basic.py,sha256=MEGWm3w6inz0KHFArFPWTJXneX7_-o53Wf38GdcWXwg,1254
matplotlib/tests/test_bbox_tight.py,sha256=4Hie2haP7to5h0h4OKe6TgCTcBXqXU5GRiHHgdn7S68,5090
matplotlib/tests/test_category.py,sha256=vrQUEqupQziRmUyuT0vyuZUM9HVxRk9Gg2A2Ka1uujM,10501
matplotlib/tests/test_cbook.py,sha256=PCHmdqO2JAdzIeSr7X5zY9OsfMIFp7WzeKd6L_QpPIA,25394
matplotlib/tests/test_collections.py,sha256=PE-L1D_z-XaGwFgnu2DOkXClJrw5pxx1HY1lHgPfEMU,23670
matplotlib/tests/test_colorbar.py,sha256=IAbC_fC_wr3gthL_ezyKIN7BwnMCQTwB7zjFeV96aRQ,23263
matplotlib/tests/test_colors.py,sha256=YxpvtFxL2fVnAkQVYcI1b9VhenWF65vXwl1uJkAc6so,41833
matplotlib/tests/test_compare_images.py,sha256=twmG-C7CB9EZpA9ogy6YrCtgH1TJZMj2sBjFaxeZx7M,3366
matplotlib/tests/test_constrainedlayout.py,sha256=bwvGUepfjWEFfQDPgz5sE6QHn7OTJRGtXtfnkYgLYW4,13453
matplotlib/tests/test_container.py,sha256=QgNodtC50-2SyP_8coGyp1nQsmLKhfivlJrfX4cujds,580
matplotlib/tests/test_contour.py,sha256=v7nnoXzpkS1sAZZ4WtQ_otYkNJz0EGAHdCF5W1EUpXE,14229
matplotlib/tests/test_cycles.py,sha256=7SnFRFaoAB9B77dtNkiDCBwr6QsQA4sSOQZxkzYZmYs,5820
matplotlib/tests/test_dates.py,sha256=0d14ZtzF6oYQUXWCQ_kg0A1qeaM_p3vP9qogUhg9d-k,39907
matplotlib/tests/test_determinism.py,sha256=vIsFXA2_4Wa3s2CxptfzjbvqYdtJASN0GjWHTEgqZr8,4729
matplotlib/tests/test_dviread.py,sha256=USbWVyR1pY5HMuoHEHWgfBaCojUQkuxLt-J4gSkwBcw,2378
matplotlib/tests/test_figure.py,sha256=zBewJVqO9xe5_XF6dadZ_2cwkMBp45l0FvqZHiz-Ocs,26386
matplotlib/tests/test_font_manager.py,sha256=ZBCXRukt1fVQVpzrExKWWAZqk4rX6XGvU_uah63uGOk,7831
matplotlib/tests/test_fontconfig_pattern.py,sha256=NeM0UxB4m4fGY2Zq84QoDGyKnEOAcgmi0Cs-VjyFY0I,2091
matplotlib/tests/test_gridspec.py,sha256=JiyMWPmsW_wJc7m4f8b2KRqCKlBSSlUt3LUyhak0heM,997
matplotlib/tests/test_image.py,sha256=Vtsqq5Y4unI29BK3OpZZsGeji--pHQJfjc8ztsFs_CM,36845
matplotlib/tests/test_legend.py,sha256=jzxsQCCdjtb77NrW63ue7EdfANyFCZv7T8fjxERrRfQ,23535
matplotlib/tests/test_lines.py,sha256=qXMwXdKo708S17A31-C8U-dCcjZfsyg3uuMdZXN-PLM,8602
matplotlib/tests/test_marker.py,sha256=ixsrer_pKNc6vU7Idu_QrlUPSgHlZgYFGut2f3w9pP4,6612
matplotlib/tests/test_mathtext.py,sha256=E0ZXbgGTazCLUXxSgifv6YmRwTPft7reW1nnUemlC14,15173
matplotlib/tests/test_matplotlib.py,sha256=FgFg2Yym_bjlb0gtG1pcs14DPyUIMB7tRVtbiMieEQo,1499
matplotlib/tests/test_mlab.py,sha256=-nRS28Dc9_XyVK3mmFRInJUc3gU9hBS6BDRaIW2-tmg,67393
matplotlib/tests/test_offsetbox.py,sha256=Kx9DM2-C6REmqFN5GTO9xDL5vYYsISsO6e3pjzz9xpY,11136
matplotlib/tests/test_patches.py,sha256=09GJ6ElUqYnSE_Areb_hZwwFfe-kLOF4DZLtPJQFMGY,19838
matplotlib/tests/test_path.py,sha256=-85BmXlJRb9GBUK3mijnbhBjzDxkKcy8E1zBdc7hGRo,16733
matplotlib/tests/test_patheffects.py,sha256=gZEisNhkiVXG20buNgTV1Z4x0sfTP-C4_wK6ylAIiBs,5336
matplotlib/tests/test_pickle.py,sha256=f_d79RREQTbn1FVFPbTqypIsnW4vxPcanwsfd-jreDo,5831
matplotlib/tests/test_png.py,sha256=W_Otlwm8zTB17LRYObVh9CdNe3hut6XV7LGbWypPJeg,1352
matplotlib/tests/test_polar.py,sha256=8l4RGgsUgKIeYmPtlu6nWzEJjjRB38xUaii3vkLKpW0,12082
matplotlib/tests/test_preprocess_data.py,sha256=D82rQiZBljpK3ggYJgcNTkszgiXGJLv11HsOm8WdHP4,10573
matplotlib/tests/test_pyplot.py,sha256=YQXJd8U460zL3wSIRhuS4WfvdeHTDOHcUd7rhVrGoPs,2564
matplotlib/tests/test_quiver.py,sha256=gyqDugeUL8gOnMXTvtd-GHI99usI7L5FDvfvLDuJCLU,8321
matplotlib/tests/test_rcparams.py,sha256=0qWFMCXfetscfFzEicsVrl9jgNZAPeUQjytuYseO6DY,19765
matplotlib/tests/test_sankey.py,sha256=Eo3IScGRi0pvO4zcFj3STd3UWSzmfXKiHkk_GZsJ2TQ,322
matplotlib/tests/test_scale.py,sha256=S7lWUqoySkltqDuon7PzPtG-CBVqBcDIWdQ8cuS6B5U,5865
matplotlib/tests/test_simplification.py,sha256=dt0mnYH7Iy059fIpG1WMd4W-mEBGrk74RtCxzgr1vkI,11393
matplotlib/tests/test_skew.py,sha256=1fVVQkXDPq9KwzpWY7RA1inpNHNmTMUNQxxvm_eKB3A,6475
matplotlib/tests/test_sphinxext.py,sha256=PcrAosOag3sZrW00QoJobUHaV28Vn4GFaSY0ljD19Y4,2023
matplotlib/tests/test_spines.py,sha256=qYJoX052KdKhtAXTIPNWEi_BqfHhVEQXhA92UcOObBg,3226
matplotlib/tests/test_streamplot.py,sha256=c9Tf85Pc2pJlWRA5AgJ64XHHbi3fsySUoDCIjUrwKnM,3936
matplotlib/tests/test_style.py,sha256=Xm9v8NSieC69Rnd_rGXUG_6SOdNPE7Gr--nMKI5nBbc,5905
matplotlib/tests/test_subplots.py,sha256=zDMNiClqpFPUsThIgfyrksP3gls_ylygz6JSvMNy8pE,6155
matplotlib/tests/test_table.py,sha256=Qs0eUsDGfNIqMMnLPDj412deLm7UxQb0wXml5fZoV-0,5925
matplotlib/tests/test_testing.py,sha256=s8XUis7Li46dxTs0ErnLdiYHmudmllzsw2B0Bcu1SCU,653
matplotlib/tests/test_texmanager.py,sha256=IoCMn9UcYSngl3m_nlWrDV9BZrNrtOiM2P5s5ULSb4w,475
matplotlib/tests/test_text.py,sha256=6hqNw62WHVi21Bm2oD-xdCRj5eyhIpWgjr-kHcaMfiA,22739
matplotlib/tests/test_ticker.py,sha256=C0pc1h7FnMG1tf9B8W4g0n9B410OacvfZGPqmubCXmQ,52438
matplotlib/tests/test_tightlayout.py,sha256=uS2TuGzXl0RyDVbxiJk68IPG6Lvdc768uEVW8bjCaZw,10634
matplotlib/tests/test_transforms.py,sha256=DjlW9PJStojWJDFQBWnFRXNvQ4HPL5SUMQUoxpIZiUk,27982
matplotlib/tests/test_triangulation.py,sha256=UwE8TMEKR_Q3tPi_3CVfN5PVGjb7hB641TiL-Drkf-g,47111
matplotlib/tests/test_ttconv.py,sha256=MwDVqn8I2a09l7sN9vfluhABCpzoPasZOzrvoadYn8o,557
matplotlib/tests/test_type1font.py,sha256=WHGvm7UADbY4Bislu7aTs-qonmYU_TyP4mCBWG6RR1Y,2139
matplotlib/tests/test_units.py,sha256=BnJsIzwucyJb7WO7cXeXs9cvow48wmXl2q0Xz6igLJs,5889
matplotlib/tests/test_usetex.py,sha256=4XmyxMcUWniqTGx0KGAGEo7E_6Qz18_qZEzWg74gOoM,2985
matplotlib/tests/test_widgets.py,sha256=W7-t8XqjYvUkif10wVsoDteQQgiu32k55tz9RDuSCTk,16623
matplotlib/tri/__init__.py,sha256=mXgRgH1EgncFGFKziNNW2C6zp0VEHtuhWZAQ6MKctJg,268
matplotlib/tri/triangulation.py,sha256=1Lw9tarP_KKZ8YQ9q_IgfPVbJrz3P86QYAK24dhOtzI,8546
matplotlib/tri/tricontour.py,sha256=J5dPX6U5uqSuVChF2k8IznFVubzicoD3abcY5wRHzmE,11635
matplotlib/tri/trifinder.py,sha256=1js01EAwh4I6uxFkQrrjozLxCJynlazZ0POamc-Ujfc,3561
matplotlib/tri/triinterpolate.py,sha256=ODGAV7QauNe5-jKvzet9wiVp3kPYyHNlmOM42zI5wkg,65986
matplotlib/tri/tripcolor.py,sha256=ySM-FzC0KRaz2XpAOh1B8x78qLRNzoa3gKSV4gC0_Rc,5138
matplotlib/tri/triplot.py,sha256=iiS5jMj9P1LCuzQm_T5PLh8UXhQqTn7NSlHquiQK1dg,2845
matplotlib/tri/trirefine.py,sha256=4mY037ghrhtOLE0_MW-f_KEbsgYXQ_buwxIGhgnYMiQ,13529
matplotlib/tri/tritools.py,sha256=3MZ4E4xgXHuOUQmY_o0Py4hJ54Dm9Q5tRwwYpBeFmAs,10842
mpl_toolkits/axes_grid/__init__.py,sha256=3oKEqnq1Mee3hQEmxRcIxO3iS1WeYE_cJ5gZoIXFqUY,548
mpl_toolkits/axes_grid/anchored_artists.py,sha256=TDvJzLlt85cfXoiT1Yk4j0DEw_6HXeV6zb-tAdPP3zs,297
mpl_toolkits/axes_grid/angle_helper.py,sha256=f77E-aQao6GkJfpEGmLAvpWgFkktkIV9d8YXVsDfsBQ,52
mpl_toolkits/axes_grid/axes_divider.py,sha256=Sa_hLFBUH6F6P4apbj_9RQQJS-LfK8kMKe1U5AvHYqE,181
mpl_toolkits/axes_grid/axes_grid.py,sha256=k7q2Tuf5yr29lDqK9DhixFgDi9R0G2ZQT__paXthg34,91
mpl_toolkits/axes_grid/axes_rgb.py,sha256=V691yLhii-qIdxPFDoRF-28h-IINq1CDHUWa_fPmqDY,48
mpl_toolkits/axes_grid/axes_size.py,sha256=SV0uHhIRHVYpGIZdw3gb8T4jvh0G24KPUPr11x9TGbY,49
mpl_toolkits/axes_grid/axis_artist.py,sha256=VuHYa0LaHdU1YKDIir03ykI64Wd1zSYhnXuUEiIatAk,51
mpl_toolkits/axes_grid/axisline_style.py,sha256=o2aVaavBc62VLXVYZCStRjGktDD8rfkwxwXTHJuKb-U,54
mpl_toolkits/axes_grid/axislines.py,sha256=JFEkMHiAfYPEK1M3atZwmnMAn6KcgoUQALk0aApbvZw,49
mpl_toolkits/axes_grid/clip_path.py,sha256=uSPvk9ovfA9UkX2fPAoLJPA4nBOEB27HaEKb4M-tpdI,49
mpl_toolkits/axes_grid/colorbar.py,sha256=o2y9Q9Vk4xziksEVaCbmlA41JleaqXITkargdew4u-U,176
mpl_toolkits/axes_grid/floating_axes.py,sha256=tQxJJwFSBxNDcZCWjLDKIQ3Ck81TkJuErE_wbbwIbN0,53
mpl_toolkits/axes_grid/grid_finder.py,sha256=YrtbbCgHY71Cowpc0TJOxTTyg-vwJSsuL0iRXogOllI,51
mpl_toolkits/axes_grid/grid_helper_curvelinear.py,sha256=yh_X2vXTRUuGbrQxynWOO400T3Sifsxl5oFmNdrinrU,63
mpl_toolkits/axes_grid/inset_locator.py,sha256=lvj8PMLvtLz0YA2KIUG6edwwI3zC0qbHo0V5ZPG-eKc,220
mpl_toolkits/axes_grid/parasite_axes.py,sha256=cH5AdjQhngqXD_9-FALVW48GwLw9LvtBMkjXY9vvoK0,448
mpl_toolkits/axes_grid1/__init__.py,sha256=Dj6jFICuj-u5Om3DuZvW_9BQC2-dXXz06xhFNZQCmlo,209
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=K36y4695wWBdr-UUJ2xWBm3StMWZH23c_SdzLHH0BMQ,20730
mpl_toolkits/axes_grid1/axes_divider.py,sha256=otSCDY3RHMqL-JH5Wvwe_ZA5vNuhMvY33qGW-ZXEep4,26561
mpl_toolkits/axes_grid1/axes_grid.py,sha256=RaoJpq3lXVAeKCaH7s2cmGh8-Yw4cjLtK2qxkDVX8d4,24226
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=78d239ySNQX3Dl03-3nwxeqjCgTYJs3ZQ9bzSU4HM4k,5339
mpl_toolkits/axes_grid1/axes_size.py,sha256=RaAcOUa4o3uGQGueeLhKDdlMn3E9hUjvXCQzmTtQBYg,7822
mpl_toolkits/axes_grid1/colorbar.py,sha256=Oi4TvfoOmI7n11HSWcwXZ9a53g7IbBZPrW-l9UHN8yM,28718
mpl_toolkits/axes_grid1/inset_locator.py,sha256=vkPbxOMbq9hzvFn1VrweexDtG_lm4xSl8HoG36NjI7Y,23765
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=zIyP1XxXI1WqIbtT2v-9BbwBdx89hdZ2Tyg-X6MaDLk,4514
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=J-j_RvJd7js57ybEslbKIEvciaVcIKb04e6tbBYqpXQ,14832
mpl_toolkits/axisartist/__init__.py,sha256=Aa_vsf6h8XO-YR-IDQckhtAN6HBFA8-3nnGEGKhV6hU,730
mpl_toolkits/axisartist/angle_helper.py,sha256=p2P2NldZ-n9EM0PAi80PU2Sl3xbPpE5UdzRRr0MgwYQ,13618
mpl_toolkits/axisartist/axes_divider.py,sha256=2ReTIrF3nwwXtxiIchWRjImPLnxeoce8J17Gd8UsbY4,129
mpl_toolkits/axisartist/axes_grid.py,sha256=-BiKRKmUY9SN2YkPwFkbsnrtDCux3HBQV2XbzOrKrrA,365
mpl_toolkits/axisartist/axes_rgb.py,sha256=a2lvQ9MxvXjh9ZXDvzM6VNBA8AGg5xngu_pKAx8PWOc,190
mpl_toolkits/axisartist/axis_artist.py,sha256=dB-QohGsgBE-ifygSGdAB1tZOeBAmiltny0WqomG8qU,43295
mpl_toolkits/axisartist/axisline_style.py,sha256=oCZdSjWsCgs3HwnF9fAWjUyN49prLDxMDXDrwzA2EoY,5192
mpl_toolkits/axisartist/axislines.py,sha256=0HXaSscHdQ_dgME8OKZGwX6XGCfScsYPLXuUAEVeZbE,20322
mpl_toolkits/axisartist/clip_path.py,sha256=fQxXkgaFroThzHHeWDFfYLoAXbYoOsZt1awRFkSPZeY,3895
mpl_toolkits/axisartist/floating_axes.py,sha256=GOhgBLbJg9kHCPwEsxIaNnGkY-XCD6DaZ16ePpesaKg,13233
mpl_toolkits/axisartist/grid_finder.py,sha256=CvALRLYWhBp3bwcQc5heThDZTiEGVHmEq7E5N9ssQS0,11271
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=CEqPWCyRbHrCSjTQhCLlEIJX4DxQXmVtkpNy5ay-stM,14656
mpl_toolkits/axisartist/parasite_axes.py,sha256=Kj-_p0dpdSgnGzElDHtmBW7u8wdka5wfn150rTD4Uy4,425
mpl_toolkits/mplot3d/__init__.py,sha256=-7jYs7BlOeVjnGxLWEfMb93i-vzMi6Hdi9CLsAWOD4k,28
mpl_toolkits/mplot3d/art3d.py,sha256=MgozhPS_h7hSM4VwBdEPGiieAN51MojaJYdRDhdSvQU,27006
mpl_toolkits/mplot3d/axes3d.py,sha256=J_r64AVS77Qex9jXyLOYpTYDjFFObyAlohggFF4wY4g,106493
mpl_toolkits/mplot3d/axis3d.py,sha256=14a9N47mqzSNHfnqoPzG8CxBTNsqqkCxBhh2AAP9fOc,19299
mpl_toolkits/mplot3d/proj3d.py,sha256=Lbc0nw5w6Cvune2_kxCwIJ9Gg5VryOD0Ilue_3lU9dY,4441
mpl_toolkits/tests/__init__.py,sha256=jY2lF4letZKOagkrt6B_HnnKouuCgo8hG3saDzq8eGI,375
mpl_toolkits/tests/conftest.py,sha256=C8DbesGlumOhSREkWrdBGaWu0vchqik17dTokP8aDAA,216
mpl_toolkits/tests/test_axes_grid.py,sha256=0mL0Wgf9QrjrhFOwDMIIKxEzK3FnbX0mS8sU4d6rp1E,2514
mpl_toolkits/tests/test_axes_grid1.py,sha256=suLYN5t9RoT6y0tkSHYh_W8AmXsRzUQUrsmd3tUiils,18571
mpl_toolkits/tests/test_axisartist_angle_helper.py,sha256=SI_lyCLbVKikZp9DRBpq--MbfT10vSQsNx1Z5HMeqMw,5811
mpl_toolkits/tests/test_axisartist_axis_artist.py,sha256=yzGoWLFPlnkCYZKMO5M6KEipnZD0PsWK5UNToX3LaVU,3107
mpl_toolkits/tests/test_axisartist_axislines.py,sha256=KUI4stZL2DRJyKdnv27czKk2qt4wuBQCOV_w59dfR-Q,2535
mpl_toolkits/tests/test_axisartist_clip_path.py,sha256=Hj622Au6sUcrIN6ryWnm9UUxXVd2isWxFZCUo1YicY0,1036
mpl_toolkits/tests/test_axisartist_floating_axes.py,sha256=91QavzOE2xtqTH8VcU68R2Uqg3IALIUI-qwZcCeaG5E,4247
mpl_toolkits/tests/test_axisartist_grid_finder.py,sha256=oNZQ1PoRgpUoWqg8qdvgplZW3iFK6FhXcVS9LxuVqZE,338
mpl_toolkits/tests/test_axisartist_grid_helper_curvelinear.py,sha256=pa5vb6wbkiv404-5b1O83IatUOObww4wbxDh9lSlZes,7729
mpl_toolkits/tests/test_mplot3d.py,sha256=AZ5JHgIb1tJEDyrXC02vi9TteF3JCn9PQ-zsuBuIqrc,36118
matplotlib-3.3.2.dist-info/LICENSE,sha256=ojr3trhyymyw7GeYxkhO2JYoAxUVscbgMFz9b1qrcVM,4928
matplotlib-3.3.2.dist-info/LICENSE_AMSFONTS,sha256=1nBvhOSH8d3ceR8cyG_bknr7Wg4RSmoB5M_HE72FVyE,12915
matplotlib-3.3.2.dist-info/LICENSE_BAKOMA,sha256=RMmLfO-TQmiuGbo4NwaPETH4OqNTWAzDwDWPTA8ikdg,1480
matplotlib-3.3.2.dist-info/LICENSE_CARLOGO,sha256=zpO9wbKCiF7rqj4STQcHWjzLj_67kzhx1mzdwjnLdIE,4499
matplotlib-3.3.2.dist-info/LICENSE_COLORBREWER,sha256=13Q--YD83BybM3nwuFLBxbUKygI9hALtJ8tZZiSQj5I,2006
matplotlib-3.3.2.dist-info/LICENSE_QT4_EDITOR,sha256=AlDgmC0knGnjFWMZHYO0xVFQ4ws699gKFMSnKueffdM,1260
matplotlib-3.3.2.dist-info/LICENSE_SOLARIZED,sha256=RrSaK9xcK12Uhuka3LOhEB_QW5ibbYX3kdwCakxULM0,1141
matplotlib-3.3.2.dist-info/LICENSE_STIX,sha256=I3calycBxqh5ggJcyDvyYU4vu6Qf2bpleUWbTmWKDL4,3985
matplotlib-3.3.2.dist-info/LICENSE_YORICK,sha256=iw-4fuTKjfpFYXIStZJ_pmLmIuZZWzUIpz6RwIKCSkk,2362
matplotlib-3.3.2.dist-info/METADATA,sha256=wU6O5V7R_ajbGQFO2-LQWfU_4bb-I7I-BfNB5gDcQ_Y,5700
matplotlib-3.3.2.dist-info/WHEEL,sha256=g8eocn77V_iXxq9laUIPSuxdwRHlhOsH3tLpu1uKUvQ,106
matplotlib-3.3.2.dist-info/namespace_packages.txt,sha256=A2PHFg9NKYOU4pEQ1h97U0Qd-rB-65W34XqC-56ZN9g,13
matplotlib-3.3.2.dist-info/top_level.txt,sha256=9tEw2ni8DdgX8CceoYHqSH1s50vrJ9SDfgtLIG8e3Y4,30
matplotlib-3.3.2.dist-info/RECORD,,
matplotlib-3.3.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib/axes/__pycache__/_axes.cpython-37.pyc,,
matplotlib/axes/__pycache__/_base.cpython-37.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-37.pyc,,
matplotlib/axes/__pycache__/_subplots.cpython-37.pyc,,
matplotlib/axes/__pycache__/__init__.cpython-37.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-37.pyc,,
matplotlib/backends/qt_editor/__pycache__/formsubplottool.cpython-37.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-37.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formsubplottool.cpython-37.pyc,,
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_qt4.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_qt4agg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_qt4cairo.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-37.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-37.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-37.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-37.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-37.pyc,,
matplotlib/backends/__pycache__/__init__.cpython-37.pyc,,
matplotlib/cbook/__pycache__/deprecation.cpython-37.pyc,,
matplotlib/cbook/__pycache__/__init__.cpython-37.pyc,,
matplotlib/compat/__pycache__/__init__.cpython-37.pyc,,
matplotlib/projections/__pycache__/geo.cpython-37.pyc,,
matplotlib/projections/__pycache__/polar.cpython-37.pyc,,
matplotlib/projections/__pycache__/__init__.cpython-37.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-37.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-37.pyc,,
matplotlib/sphinxext/__pycache__/__init__.cpython-37.pyc,,
matplotlib/style/__pycache__/core.cpython-37.pyc,,
matplotlib/style/__pycache__/__init__.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-37.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-37.pyc,,
matplotlib/testing/__pycache__/compare.cpython-37.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-37.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-37.pyc,,
matplotlib/testing/__pycache__/disable_internet.cpython-37.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-37.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-37.pyc,,
matplotlib/testing/__pycache__/__init__.cpython-37.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-37.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-37.pyc,,
matplotlib/tests/__pycache__/__init__.cpython-37.pyc,,
matplotlib/tri/__pycache__/triangulation.cpython-37.pyc,,
matplotlib/tri/__pycache__/tricontour.cpython-37.pyc,,
matplotlib/tri/__pycache__/trifinder.cpython-37.pyc,,
matplotlib/tri/__pycache__/triinterpolate.cpython-37.pyc,,
matplotlib/tri/__pycache__/tripcolor.cpython-37.pyc,,
matplotlib/tri/__pycache__/triplot.cpython-37.pyc,,
matplotlib/tri/__pycache__/trirefine.cpython-37.pyc,,
matplotlib/tri/__pycache__/tritools.cpython-37.pyc,,
matplotlib/tri/__pycache__/__init__.cpython-37.pyc,,
matplotlib/__pycache__/afm.cpython-37.pyc,,
matplotlib/__pycache__/animation.cpython-37.pyc,,
matplotlib/__pycache__/artist.cpython-37.pyc,,
matplotlib/__pycache__/axis.cpython-37.pyc,,
matplotlib/__pycache__/backend_bases.cpython-37.pyc,,
matplotlib/__pycache__/backend_managers.cpython-37.pyc,,
matplotlib/__pycache__/backend_tools.cpython-37.pyc,,
matplotlib/__pycache__/bezier.cpython-37.pyc,,
matplotlib/__pycache__/blocking_input.cpython-37.pyc,,
matplotlib/__pycache__/category.cpython-37.pyc,,
matplotlib/__pycache__/cm.cpython-37.pyc,,
matplotlib/__pycache__/collections.cpython-37.pyc,,
matplotlib/__pycache__/colorbar.cpython-37.pyc,,
matplotlib/__pycache__/colors.cpython-37.pyc,,
matplotlib/__pycache__/container.cpython-37.pyc,,
matplotlib/__pycache__/contour.cpython-37.pyc,,
matplotlib/__pycache__/dates.cpython-37.pyc,,
matplotlib/__pycache__/docstring.cpython-37.pyc,,
matplotlib/__pycache__/dviread.cpython-37.pyc,,
matplotlib/__pycache__/figure.cpython-37.pyc,,
matplotlib/__pycache__/fontconfig_pattern.cpython-37.pyc,,
matplotlib/__pycache__/font_manager.cpython-37.pyc,,
matplotlib/__pycache__/gridspec.cpython-37.pyc,,
matplotlib/__pycache__/hatch.cpython-37.pyc,,
matplotlib/__pycache__/image.cpython-37.pyc,,
matplotlib/__pycache__/legend.cpython-37.pyc,,
matplotlib/__pycache__/legend_handler.cpython-37.pyc,,
matplotlib/__pycache__/lines.cpython-37.pyc,,
matplotlib/__pycache__/markers.cpython-37.pyc,,
matplotlib/__pycache__/mathtext.cpython-37.pyc,,
matplotlib/__pycache__/mlab.cpython-37.pyc,,
matplotlib/__pycache__/offsetbox.cpython-37.pyc,,
matplotlib/__pycache__/patches.cpython-37.pyc,,
matplotlib/__pycache__/path.cpython-37.pyc,,
matplotlib/__pycache__/patheffects.cpython-37.pyc,,
matplotlib/__pycache__/pylab.cpython-37.pyc,,
matplotlib/__pycache__/pyplot.cpython-37.pyc,,
matplotlib/__pycache__/quiver.cpython-37.pyc,,
matplotlib/__pycache__/rcsetup.cpython-37.pyc,,
matplotlib/__pycache__/sankey.cpython-37.pyc,,
matplotlib/__pycache__/scale.cpython-37.pyc,,
matplotlib/__pycache__/spines.cpython-37.pyc,,
matplotlib/__pycache__/stackplot.cpython-37.pyc,,
matplotlib/__pycache__/streamplot.cpython-37.pyc,,
matplotlib/__pycache__/table.cpython-37.pyc,,
matplotlib/__pycache__/texmanager.cpython-37.pyc,,
matplotlib/__pycache__/text.cpython-37.pyc,,
matplotlib/__pycache__/textpath.cpython-37.pyc,,
matplotlib/__pycache__/ticker.cpython-37.pyc,,
matplotlib/__pycache__/tight_bbox.cpython-37.pyc,,
matplotlib/__pycache__/tight_layout.cpython-37.pyc,,
matplotlib/__pycache__/transforms.cpython-37.pyc,,
matplotlib/__pycache__/ttconv.cpython-37.pyc,,
matplotlib/__pycache__/type1font.cpython-37.pyc,,
matplotlib/__pycache__/units.cpython-37.pyc,,
matplotlib/__pycache__/widgets.cpython-37.pyc,,
matplotlib/__pycache__/_animation_data.cpython-37.pyc,,
matplotlib/__pycache__/_cm.cpython-37.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-37.pyc,,
matplotlib/__pycache__/_color_data.cpython-37.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-37.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-37.pyc,,
matplotlib/__pycache__/_layoutbox.cpython-37.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-37.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-37.pyc,,
matplotlib/__pycache__/_text_layout.cpython-37.pyc,,
matplotlib/__pycache__/_version.cpython-37.pyc,,
matplotlib/__pycache__/__init__.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/anchored_artists.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/angle_helper.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_divider.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_grid.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_rgb.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axes_size.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axislines.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axisline_style.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/axis_artist.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/clip_path.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/colorbar.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/floating_axes.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/grid_finder.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/grid_helper_curvelinear.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/inset_locator.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/parasite_axes.cpython-37.pyc,,
mpl_toolkits/axes_grid/__pycache__/__init__.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/colorbar.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-37.pyc,,
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/clip_path.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-37.pyc,,
mpl_toolkits/axisartist/__pycache__/__init__.cpython-37.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-37.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-37.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-37.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-37.pyc,,
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/conftest.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axes_grid.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axes_grid1.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_angle_helper.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_axislines.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_axis_artist.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_clip_path.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_floating_axes.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_grid_finder.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_axisartist_grid_helper_curvelinear.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/test_mplot3d.cpython-37.pyc,,
mpl_toolkits/tests/__pycache__/__init__.cpython-37.pyc,,
__pycache__/pylab.cpython-37.pyc,,
