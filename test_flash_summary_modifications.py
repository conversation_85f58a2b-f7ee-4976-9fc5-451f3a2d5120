#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Flash品质测试用例及报告汇总.xlsx 专用的 SMART 信息过滤和红色标注功能
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们修改的模块
import PublicFuc
import FlashSummary

def test_flash_summary_smart_filtering():
    """测试 Flash品质测试用例及报告汇总.xlsx 专用的 SMART 信息过滤功能"""
    print("=== 测试 Flash品质测试用例及报告汇总.xlsx 专用 SMART 信息过滤功能 ===")
    
    # 测试用例1：包含允许和不允许的字段
    test_smart1 = "05=1234,0C=5678,AF=ABCD,B2=EFGH,XX=9999,C3=1111"
    filtered1 = PublicFuc.FilterSmartInfoForFlashSummary(test_smart1)
    print(f"输入: {test_smart1}")
    print(f"过滤后: {filtered1}")
    print(f"预期包含: 05, AF, B2, C3 (不包含0C和XX)")
    print()
    
    # 测试用例2：只包含允许的字段
    test_smart2 = "B5=2222,B6=3333,C4=4444,C5=5555,C6=6666,C7=7777"
    filtered2 = PublicFuc.FilterSmartInfoForFlashSummary(test_smart2)
    print(f"输入: {test_smart2}")
    print(f"过滤后: {filtered2}")
    print(f"预期: 全部保留")
    print()
    
    # 测试用例3：空字符串
    test_smart3 = ""
    filtered3 = PublicFuc.FilterSmartInfoForFlashSummary(test_smart3)
    print(f"输入: '{test_smart3}'")
    print(f"过滤后: '{filtered3}'")
    print(f"预期: 空字符串")
    print()
    
    # 测试用例4：不包含允许的字段
    test_smart4 = "XX=1111,YY=2222,ZZ=3333,0C=4444"
    filtered4 = PublicFuc.FilterSmartInfoForFlashSummary(test_smart4)
    print(f"输入: {test_smart4}")
    print(f"过滤后: '{filtered4}'")
    print(f"预期: 空字符串（0C不在允许列表中）")
    print()

def test_flash_summary_abnormal_detection():
    """测试 Flash品质测试用例及报告汇总.xlsx 专用的异常 SMART 字段检测功能"""
    print("=== 测试 Flash品质测试用例及报告汇总.xlsx 专用异常 SMART 字段检测功能 ===")
    
    # 测试用例1：包含异常字段（非零值）
    test_smart1 = "05=1234,AF=0000,B2=EFGH"
    has_abnormal1 = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_smart1)
    print(f"输入: {test_smart1}")
    print(f"检测结果: {has_abnormal1}")
    print(f"预期: True (05和B2是异常字段且非零)")
    print()
    
    # 测试用例2：包含异常字段但值为零
    test_smart2 = "05=0000,B2=00,AF=1234"
    has_abnormal2 = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_smart2)
    print(f"输入: {test_smart2}")
    print(f"检测结果: {has_abnormal2}")
    print(f"预期: False (异常字段值为零)")
    print()
    
    # 测试用例3：不包含异常字段
    test_smart3 = "AF=1234,C3=5678,C6=9999"
    has_abnormal3 = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_smart3)
    print(f"输入: {test_smart3}")
    print(f"检测结果: {has_abnormal3}")
    print(f"预期: False (不包含异常字段)")
    print()
    
    # 测试用例4：包含多个异常字段
    test_smart4 = "B5=1111,B6=2222,C4=3333,C5=4444"
    has_abnormal4 = PublicFuc.HasAbnormalSmartFieldsForFlashSummary(test_smart4)
    print(f"输入: {test_smart4}")
    print(f"检测结果: {has_abnormal4}")
    print(f"预期: True (包含多个异常字段)")
    print()

def test_flash_summary_get_all_smart_info():
    """测试 FlashSummary.py 中的 GetAllSmartInfo 函数"""
    print("=== 测试 FlashSummary.py 中的 GetAllSmartInfo 函数 ===")
    
    # 模拟从 Excel 读取的数据
    test_data = [
        ["Sample001", "05=1234,0C=5678,AF=ABCD,B2=EFGH,XX=9999"],
        ["Sample002", "B5=2222,B6=3333,YY=7777"],
        ["Sample003", "C4=4444,C5=5555,C6=6666,C7=7777"],
        ["Sample004", ""],  # 空 SMART 信息
        ["Sample005", "XX=1111,YY=2222,0C=3333"]  # 不包含允许的字段
    ]
    
    result = FlashSummary.GetAllSmartInfo(test_data)
    print("输入数据:")
    for data in test_data:
        print(f"  {data}")
    print()
    print("处理结果:")
    print(result)
    print()
    print("预期:")
    print("- Sample001: 只包含 05, AF, B2")
    print("- Sample002: 只包含 B5, B6")
    print("- Sample003: 包含 C4, C5, C6, C7")
    print("- Sample004: 跳过（空信息）")
    print("- Sample005: 跳过（无允许字段）")
    print()

def test_flash_summary_constants():
    """测试 PublicFuc.py 中的常量定义"""
    print("=== 测试 PublicFuc.py 中的常量定义 ===")
    
    print(f"flashSummarySmartKey: {PublicFuc.flashSummarySmartKey}")
    print(f"flashSummaryAbnormalKey: {PublicFuc.flashSummaryAbnormalKey}")
    print()
    
    # 验证异常字段是过滤字段的子集
    abnormal_in_summary = all(key in PublicFuc.flashSummarySmartKey for key in PublicFuc.flashSummaryAbnormalKey)
    print(f"异常字段是否都在过滤字段中: {abnormal_in_summary}")
    print(f"预期: True")
    print()

def test_get_new_mars_dic_for_flash_summary():
    """测试 PublicFuc.py 中的 GetNewMarsDicForFlashSummary 函数"""
    print("=== 测试 PublicFuc.py 中的 GetNewMarsDicForFlashSummary 函数 ===")
    
    # 模拟输入数据
    test_data = {
        'Sample001': {
            'MMS_PC': 'PC001',
            'capacity': '256',
            'test_result': 'PASS',
            'end_circle': '100',
            'start_circle': '0',
            'end_id_f1': '0x1000',
            'start_id_f1': '0x0000',
            'end_id_f2': '0x2000',
            'start_id_f2': '0x0000',
            'end_time': '2023-01-01 12:00:00',
            'start_time': '2023-01-01 10:00:00',
            'id_05': '0x1234',  # 应该保留
            'id_0c': '0x5678',  # 不应该保留
            'id_af': '0xABCD',  # 应该保留
            'id_b2': '0xEFGH',  # 应该保留
            'id_a5': '0x1000',
            'id_a6': '0x0800',
            'average_write_vel': '100',
            'max_write_vel': '120',
            'min_write_vel': '80',
            'average_read_vel': '200',
            'max_read_vel': '220',
            'min_read_vel': '180',
            'write_overtime': '0'
        }
    }
    
    keyLst = ['pc_no','cap','result','cycle','wrdata','runtime','SmartInfo','wavg','wmax','wmin','ravg','rmax','rmin','wovertime','A5-A6']
    
    try:
        result = PublicFuc.GetNewMarsDicForFlashSummary(test_data, keyLst)
        print("函数调用成功")
        print(f"结果键: {list(result.keys())}")
        if 'Sample001' in result:
            smart_info = result['Sample001'][6]  # SmartInfo 在第7个位置（索引6）
            print(f"SMART信息: {smart_info}")
            print(f"预期: 只包含 05, AF, B2 字段")
        print()
    except Exception as e:
        print(f"函数调用失败: {e}")
        print()

if __name__ == "__main__":
    print("开始测试 Flash品质测试用例及报告汇总.xlsx 专用功能\n")
    
    test_flash_summary_constants()
    test_flash_summary_smart_filtering()
    test_flash_summary_abnormal_detection()
    test_flash_summary_get_all_smart_info()
    test_get_new_mars_dic_for_flash_summary()
    
    print("测试完成！")
    print("\n=== 总结 ===")
    print("1. 已在 PublicFuc.py 中添加专门用于 Flash品质测试用例及报告汇总.xlsx 的函数")
    print("2. 已修改 FlashSummary.py 使用新的过滤和异常检测函数")
    print("3. 已修改 Flash_Reliability.py 使用新的 SMART 过滤逻辑")
    print("4. 所有修改都是新增函数，不影响其他报告文件的处理")
    print("5. 实现了对主页第5-14行L列数据的异常字段红色标注")
